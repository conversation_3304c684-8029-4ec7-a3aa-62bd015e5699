#/** @file
#
#  Copyright (c) 2016 Linaro Ltd. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmMmuPeiLib
  FILE_GUID                      = b50d8d53-1ad1-44ea-9e69-8c89d4a6d08b
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmMmuLib|PEIM
  CONSTRUCTOR                    = ArmMmuPeiLibConstructor

[Sources.AARCH64]
  ArmMmuLibInternal.h
  AArch64/ArmMmuLibCore.c
  AArch64/ArmMmuPeiLibConstructor.c
  AArch64/ArmMmuLibReplaceEntry.S

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  CacheMaintenanceLib
  HobLib
  MemoryAllocationLib

[Guids]
  gArmMmuReplaceLiveTranslationEntryFuncGuid
