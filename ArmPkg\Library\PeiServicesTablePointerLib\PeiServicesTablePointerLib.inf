## @file
# Instance of PEI Services Table Pointer Library using global variable for the table pointer.
#
# PEI Services Table Pointer Library implementation that retrieves a pointer to the
#  PEI Services Table from a global variable. Not available to modules that execute from
#  read-only memory.
#
# Copyright (c) 2007 - 2018, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2011 Hewlett-Packard Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeiServicesTablePointerLib
  FILE_GUID                      = C3C9C4ED-EB8A-4548-BE1B-ABB0B6F35B1E
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PeiServicesTablePointerLib|PEIM PEI_CORE SEC

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC (EBC is for build only)
#

[Sources]
  PeiServicesTablePointer.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  DebugLib

[Pcd]

