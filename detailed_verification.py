#!/usr/bin/env python3
"""
详细验证TDX Event Log中UEFI_VARIABLE_DATA的SHA384 digest
包含完整的数据解析和验证步骤
"""

import hashlib
import binascii
import struct

def parse_event_log_raw_data():
    """解析完整的事件日志原始数据"""
    
    print("=== 完整事件日志原始数据解析 ===\n")
    
    # 完整的RAW DATA (从事件日志中提取)
    raw_event_data = (
        "01000000" +  # RTMR Index (4 bytes)
        "01000080" +  # Event Type (4 bytes) 
        "01000000" +  # Algorithm Count (4 bytes)
        "0C00" +      # Algorithm ID (2 bytes)
        # SHA384 Digest (48 bytes)
        "9DC3A1F80BCEC915391DCDA5FFBB15E7419F77EAB462BBF72B42166FB70D50325E37B36F93537A863769BCF9BEDAE6FB" +
        "34000000" +  # Event Size (4 bytes)
        # UEFI_VARIABLE_DATA (52 bytes)
        "61DFE48BCA93D211AA0D00E098032B8C" +  # VariableName GUID
        "0A00000000000000" +                    # UnicodeNameLength  
        "0000000000000000" +                    # VariableDataLength
        "53006500630075007200650042006F006F007400"  # UnicodeName
    )
    
    data = binascii.unhexlify(raw_event_data)
    offset = 0
    
    # 解析事件头
    rtmr_index = struct.unpack('<I', data[offset:offset+4])[0]
    offset += 4
    
    event_type = struct.unpack('<I', data[offset:offset+4])[0] 
    offset += 4
    
    alg_count = struct.unpack('<I', data[offset:offset+4])[0]
    offset += 4
    
    alg_id = struct.unpack('<H', data[offset:offset+2])[0]
    offset += 2
    
    digest = data[offset:offset+48]
    offset += 48
    
    event_size = struct.unpack('<I', data[offset:offset+4])[0]
    offset += 4
    
    # 提取UEFI_VARIABLE_DATA
    uefi_var_data = data[offset:offset+event_size]
    
    print(f"RTMR Index: {rtmr_index}")
    print(f"Event Type: 0x{event_type:08X} (EV_EFI_VARIABLE_DRIVER_CONFIG)")
    print(f"Algorithm Count: {alg_count}")
    print(f"Algorithm ID: {alg_id} (TPM_ALG_SHA384)")
    print(f"Digest: {digest.hex().upper()}")
    print(f"Event Size: {event_size} bytes")
    print(f"UEFI_VARIABLE_DATA: {uefi_var_data.hex().upper()}")
    
    return uefi_var_data, digest.hex()

def parse_uefi_variable_data(data):
    """解析UEFI_VARIABLE_DATA结构"""
    
    print("\n=== UEFI_VARIABLE_DATA 结构详细解析 ===\n")
    
    offset = 0
    
    # VariableName GUID (16 bytes)
    var_guid = data[offset:offset+16]
    offset += 16
    
    # UnicodeNameLength (8 bytes)
    name_length = struct.unpack('<Q', data[offset:offset+8])[0]
    offset += 8
    
    # VariableDataLength (8 bytes) 
    data_length = struct.unpack('<Q', data[offset:offset+8])[0]
    offset += 8
    
    # UnicodeName (name_length * 2 bytes)
    unicode_name_bytes = data[offset:offset+name_length*2]
    offset += name_length * 2
    
    # VariableData (data_length bytes)
    if data_length > 0:
        variable_data = data[offset:offset+data_length]
    else:
        variable_data = b''
    
    # 解析GUID
    guid_parts = struct.unpack('<IHH8B', var_guid)
    guid_str = f"{guid_parts[0]:08X}-{guid_parts[1]:04X}-{guid_parts[2]:04X}-"
    guid_str += f"{guid_parts[3]:02X}{guid_parts[4]:02X}-"
    for i in range(5, 11):
        guid_str += f"{guid_parts[i]:02X}"
    
    # 解析Unicode名称
    unicode_name = unicode_name_bytes.decode('utf-16le')
    
    print(f"VariableName GUID: {guid_str}")
    print(f"  -> gEfiGlobalVariableGuid")
    print(f"UnicodeNameLength: {name_length} characters")
    print(f"VariableDataLength: {data_length} bytes")
    print(f"UnicodeName: '{unicode_name}'")
    print(f"VariableData: {'<empty>' if data_length == 0 else variable_data.hex()}")
    
    # 验证GUID
    expected_guid = "8BE4DF61-93CA-11D2-AA0D-00E098032B8C"
    guid_match = guid_str == expected_guid
    print(f"\nGUID验证: {'✓' if guid_match else '✗'} ({expected_guid})")
    
    return {
        'guid': guid_str,
        'name_length': name_length,
        'data_length': data_length, 
        'unicode_name': unicode_name,
        'variable_data': variable_data,
        'raw_data': data
    }

def verify_sha384_digest(data, expected_digest):
    """验证SHA384摘要"""
    
    print("\n=== SHA384 Digest 验证 ===\n")
    
    # 计算SHA384
    calculated_digest = hashlib.sha384(data).hexdigest()
    
    print(f"输入数据长度: {len(data)} bytes")
    print(f"输入数据: {data.hex().upper()}")
    print()
    print(f"计算的Digest: {calculated_digest.upper()}")
    print(f"期望的Digest: {expected_digest.upper()}")
    
    match = calculated_digest.upper() == expected_digest.upper()
    print(f"验证结果: {'✓ 匹配' if match else '✗ 不匹配'}")
    
    return match

def analyze_secure_boot_implications(var_info):
    """分析SecureBoot变量的安全含义"""
    
    print("\n=== SecureBoot 变量安全分析 ===\n")
    
    print("变量信息:")
    print(f"  名称: {var_info['unicode_name']}")
    print(f"  GUID: {var_info['guid']}")
    print(f"  数据长度: {var_info['data_length']} bytes")
    
    if var_info['data_length'] == 0:
        print("\n状态分析:")
        print("  ✓ 变量数据长度为0")
        print("  → 可能情况1: SecureBoot变量不存在 (Setup Mode)")
        print("  → 可能情况2: SecureBoot变量值为0 (Secure Boot禁用)")
        print("  → 可能情况3: 变量存在但为空值")
    else:
        var_value = var_info['variable_data'][0] if len(var_info['variable_data']) > 0 else None
        if var_value == 1:
            print("  ✓ Secure Boot 启用")
        elif var_value == 0:
            print("  ✓ Secure Boot 禁用")
        else:
            print(f"  ? 未知状态值: {var_value}")
    
    print("\n度量意义:")
    print("  ✓ 确保SecureBoot状态被可信记录")
    print("  ✓ 防止恶意修改安全启动配置")
    print("  ✓ 建立可信启动链的基础")

def main():
    """主验证流程"""
    
    print("TDX Event Log UEFI_VARIABLE_DATA 完整验证")
    print("=" * 50)
    
    # 1. 解析原始事件数据
    uefi_var_data, expected_digest = parse_event_log_raw_data()
    
    # 2. 解析UEFI_VARIABLE_DATA结构
    var_info = parse_uefi_variable_data(uefi_var_data)
    
    # 3. 验证SHA384摘要
    digest_valid = verify_sha384_digest(uefi_var_data, expected_digest)
    
    # 4. 分析安全含义
    analyze_secure_boot_implications(var_info)
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("验证总结:")
    print(f"  数据结构解析: ✓")
    print(f"  GUID验证: ✓")
    print(f"  SHA384验证: {'✓' if digest_valid else '✗'}")
    print(f"  整体验证: {'✓ 通过' if digest_valid else '✗ 失败'}")

if __name__ == "__main__":
    main()
