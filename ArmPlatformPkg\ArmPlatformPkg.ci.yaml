## @file
# CI configuration for ArmPlatformPkg
#
# Copyright (c) 2021, Arm Limited. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "ArmPlatformPkg.dsc",
    },
    ## options defined .pytool/Plugin/LicenseCheck
    "LicenseCheck": {
        "IgnoreFiles": []
    },

    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": [
            "Scripts/Ds5/",
            "Drivers/PL061GpioDxe/PL061Gpio.c"
        ]
    },

    ## options defined .pytool/Plugin/CompilerPlugin
    "CompilerPlugin": {
        "DscPath": "ArmPlatformPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestCompilerPlugin
    "HostUnitTestCompilerPlugin": {
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/CharEncodingCheck
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/DependencyCheck
    "DependencyCheck": {
        "AcceptableDependencies": [
            "ArmPlatformPkg/ArmPlatformPkg.dec",
            "ArmPkg/ArmPkg.dec",
            "EmbeddedPkg/EmbeddedPkg.dec",
            "MdeModulePkg/MdeModulePkg.dec",
            "MdePkg/MdePkg.dec"
        ],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[
            "UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec"
        ],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[],
        "IgnoreInf": []
    },

    ## options defined .pytool/Plugin/DscCompleteCheck
    "DscCompleteCheck": {
        "IgnoreInf": [],
        "DscPath": "ArmPlatformPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestDscCompleteCheck
    "HostUnitTestDscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/GuidCheck
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": [],
    },

    ## options defined .pytool/Plugin/LibraryClassCheck
    "LibraryClassCheck": {
        "IgnoreHeaderFile": []
    },

    ## options defined .pytool/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": True,
        "IgnoreFiles": [],           # use gitignore syntax to ignore errors
                                     # in matching files
        "ExtendWords": [
            "hdlcd",
            "icdsgir",
            "primecells"
           ],           # words to extend to the dictionary for this package
        "IgnoreStandardPaths": [    # Standard Plugin defined paths that
            "*.asm", "*.s"          # should be ignore
        ],
        "AdditionalIncludePaths": [] # Additional paths to spell check
                                     # (wildcards supported)
    }
}
