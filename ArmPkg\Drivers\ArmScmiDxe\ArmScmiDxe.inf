#/** @file
#
#  Copyright (c) 2017-2021, Arm Limited. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#  System Control and Management Interface V1.0
#    http://infocenter.arm.com/help/topic/com.arm.doc.den0056a/
#    DEN0056A_System_Control_and_Management_Interface.pdf
#**/

[Defines]
  INF_VERSION                    = 0x00010019
  BASE_NAME                      = ArmScmiDxe
  FILE_GUID                      = 9585984C-F027-45E9-AFDF-ADAA6DFAAAC7
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ArmScmiDxeEntryPoint

[Sources.common]
  ArmScmiBaseProtocolPrivate.h
  ArmScmiClockProtocolPrivate.h
  ArmScmiPerformanceProtocolPrivate.h
  ScmiBaseProtocol.c
  Scmi.c
  ScmiClockProtocol.c
  ScmiDxe.c
  ScmiDxe.h
  ScmiPerformanceProtocol.c
  ScmiPrivate.h

[Packages]
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  ArmMtlLib
  DebugLib
  IoLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint

[Protocols]
  gArmScmiBaseProtocolGuid
  gArmScmiClockProtocolGuid
  gArmScmiClock2ProtocolGuid
  gArmScmiPerformanceProtocolGuid

[Depex]
  TRUE

