## @file
#  Produces the CPU I/O 2 Protocol by using the services of the I/O Library.
#
# Copyright (c) 2009 - 2014, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2016, Linaro Ltd. All rights reserved.<BR>
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmPciCpuIo2Dxe
  FILE_GUID                      = 168D1A6E-F4A5-448A-9E95-795661BB3067
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ArmPciCpuIo2Initialize

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM AARCH64
#

[Sources]
  ArmPciCpuIo2Dxe.c

[Packages]
  ArmPkg/ArmPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  BaseLib
  DebugLib
  IoLib
  PcdLib
  UefiBootServicesTableLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPciIoTranslation

[Protocols]
  gEfiCpuIo2ProtocolGuid                         ## PRODUCES

[Depex]
  TRUE
