#!/usr/bin/env python3
"""
验证TDX Event Log中UEFI_VARIABLE_DATA的SHA384 digest
"""

import hashlib
import binascii

def verify_uefi_variable_digest():
    """验证SecureBoot变量的SHA384 digest"""
    
    # 从事件日志中提取的期望digest
    expected_digest = "9dc3a1f80bcec915391dcda5ffbb15e7419f77eab462bbf72b42166fb70d50325e37b36f93537a863769bcf9bedae6fb"
    
    # UEFI_VARIABLE_DATA结构的原始数据 (52 bytes)
    # 按照结构顺序组装数据
    raw_data_hex = (
        # VariableName: gEfiGlobalVariableGuid (16 bytes)
        "61DFE48BCA93D211AA0D00E098032B8C" +
        
        # UnicodeNameLength: 10 (8 bytes, little-endian)
        "0A00000000000000" +
        
        # VariableDataLength: 0 (8 bytes, little-endian) 
        "0000000000000000" +
        
        # UnicodeName: "SecureBoot" (20 bytes, UTF-16LE)
        "53006500630075007200650042006F006F007400"
    )
    
    print("=== TDX Event Log UEFI_VARIABLE_DATA Digest 验证 ===\n")
    
    # 转换为字节数组
    try:
        raw_data = binascii.unhexlify(raw_data_hex)
        print(f"输入数据长度: {len(raw_data)} bytes")
        print(f"期望长度: 52 bytes")
        print(f"长度匹配: {'✓' if len(raw_data) == 52 else '✗'}\n")
        
        # 显示数据结构
        print("UEFI_VARIABLE_DATA 结构解析:")
        print(f"  VariableName (16 bytes):     {raw_data[0:16].hex().upper()}")
        print(f"  UnicodeNameLength (8 bytes): {raw_data[16:24].hex().upper()}")
        print(f"  VariableDataLength (8 bytes):{raw_data[24:32].hex().upper()}")
        print(f"  UnicodeName (20 bytes):      {raw_data[32:52].hex().upper()}")
        print()
        
        # 计算SHA384
        sha384_hash = hashlib.sha384(raw_data).hexdigest()
        
        print("Digest 计算结果:")
        print(f"  计算得到: {sha384_hash}")
        print(f"  事件日志: {expected_digest}")
        print(f"  匹配结果: {'✓ 验证成功' if sha384_hash == expected_digest else '✗ 验证失败'}")
        
        return sha384_hash == expected_digest
        
    except Exception as e:
        print(f"错误: {e}")
        return False

def analyze_unicode_name():
    """分析Unicode变量名"""
    print("\n=== Unicode 变量名分析 ===")
    
    # SecureBoot的UTF-16LE编码
    unicode_hex = "53006500630075007200650042006F006F007400"
    
    try:
        unicode_bytes = binascii.unhexlify(unicode_hex)
        # 解码UTF-16LE
        variable_name = unicode_bytes.decode('utf-16le')
        print(f"变量名: '{variable_name}'")
        print(f"长度: {len(variable_name)} 字符")
        print(f"字节长度: {len(unicode_bytes)} bytes")
        
        # 逐字符分析
        print("字符分解:")
        for i in range(0, len(unicode_bytes), 2):
            char_bytes = unicode_bytes[i:i+2]
            char = char_bytes.decode('utf-16le')
            print(f"  {char_bytes.hex().upper()} -> '{char}'")
            
    except Exception as e:
        print(f"Unicode解析错误: {e}")

def main():
    """主函数"""
    success = verify_uefi_variable_digest()
    analyze_unicode_name()
    
    print(f"\n=== 验证结果 ===")
    print(f"Digest验证: {'通过' if success else '失败'}")

if __name__ == "__main__":
    main()
