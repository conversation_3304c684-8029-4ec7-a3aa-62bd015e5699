#/** @file
#
# Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
# Portions copyright (c) 2011 - 2014, ARM Limited. All rights reserved.
# Copyright (c) 2016, Linaro Ltd. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmBaseLib
  FILE_GUID                      = f1d943b6-99c5-46d5-af5a-66ec67662700
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmLib

[Sources]
  ArmLibPrivate.h
  ArmLib.c

[Sources.ARM]
  Arm/ArmV7Lib.h
  Arm/ArmV7Lib.c

  Arm/ArmLibSupport.S           | GCC
  Arm/ArmLibSupportV7.S         | GCC
  Arm/ArmV7Support.S            | GCC
  Arm/ArmV7ArchTimerSupport.S   | GCC

[Sources.AARCH64]
  AArch64/AArch64Lib.h
  AArch64/AArch64Lib.c

  AArch64/ArmLibSupport.S
  AArch64/ArmLibSupportV8.S
  AArch64/AArch64Support.S
  AArch64/AArch64ArchTimerSupport.S

[LibraryClasses]
  DebugLib

[Packages]
  ArmPkg/ArmPkg.dec
  MdePkg/MdePkg.dec

[FeaturePcd.ARM]
  gArmTokenSpaceGuid.PcdNormalMemoryNonshareableOverride
