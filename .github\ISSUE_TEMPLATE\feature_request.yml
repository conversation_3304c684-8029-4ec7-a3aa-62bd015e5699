# TianoCore edk2 GitHub Feature Request Template
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
#

name: 🚀 Feature Request
description: Request a feature change
title: "[Feature]: <title>"
labels: ["type:feature-request", "state:needs-triage"]

body:
  - type: markdown
    attributes:
      value: |
        👋 Thanks for taking the time to help us improve our features!

  - type: textarea
    id: feature_overview
    attributes:
      label: Feature Overview
      description: Provide a high-level summary of your feature request.
    validations:
      required: true

  - type: textarea
    id: solution_overview
    attributes:
      label: Solution Overview
      description: Give a clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    id: alternatives_considered
    attributes:
      label: Alternatives Considered
      description: Describe alternatives you've considered.
    validations:
      required: false

  - type: dropdown
    id: packages_impacted
    attributes:
      label: What packages are impacted?
      description: |
        *Select all that apply*
      multiple: true
      options:
        - ArmPkg
        - ArmPlatformPkg
        - ArmVirtPkg
        - BaseTools
        - Build or CI Code
        - CryptoPkg
        - DynamicTablesPkg
        - EmbeddedPkg
        - EmulatorPkg
        - FatPkg
        - FmpDevicePkg
        - IntelFsp2Pkg
        - IntelFsp2WrapperPkg
        - MdeModulePkg
        - MdePkg
        - NetworkPkg
        - OvmfPkg
        - PcAtChipsetPkg
        - PrmPkg
        - RedfishPkg
        - SecurityPkg
        - ShellPkg
        - SignedCapsulePkg
        - SourceLevelDebugPkg
        - StandaloneMmPkg
        - UefiCpuPkg
        - UefiPayloadPkg
        - UnitTestFrameworkPkg
        - Other
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        **Urgency Key**
        - 🟢 **Low**
          - A minor enhancement
          - It is not important to address this request in a specific time frame
        - 🟡 **Medium**
          - An important enhancement
          - Will be prioritized above *low* requests in the normal course of development
        - 🔥 **High**
          - A critical enhancement with significant value
          - Should be prioritized above *low* and *medium* requests

  - type: dropdown
    id: urgency
    attributes:
      label: Urgency
      description: How urgent is it to resolve this feature request?
      multiple: false
      options:
        - Low
        - Medium
        - High
    validations:
      required: true

  - type: dropdown
    id: request_owner
    attributes:
      label: Are you going to implement the feature request?
      description: Indicate if you are going to do the work to close this feature request.
      multiple: false
      options:
        - I will implement the feature
        - Someone else needs to implement the feature
    validations:
      required: true

  - type: dropdown
    id: needs_maintainer_feedback
    attributes:
      label: Do you need maintainer feedback?
      description: Indicate if you would like a maintainer to provide feedback on this submission.
      multiple: false
      options:
        - No maintainer feedback needed
        - Maintainer feedback requested
    validations:
      required: true

  - type: textarea
    id: anything_else
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the feature you are requesting.

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
