#/** @file
#  FDT client library for ARM's PL031 RTC driver
#
#  Copyright (c) 2016, Linaro Ltd. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmVirtPL031FdtClientLib
  FILE_GUID                      = 13173319-B270-4669-8592-3BB2B31E9E29
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmVirtPL031FdtClientLib|DXE_DRIVER DXE_RUNTIME_DRIVER
  CONSTRUCTOR                    = ArmVirtPL031FdtClientLibConstructor

[Sources]
  ArmVirtPL031FdtClientLib.c

[Packages]
  ArmPlatformPkg/ArmPlatformPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  PcdLib
  UefiBootServicesTableLib

[Protocols]
  gFdtClientProtocolGuid                                ## CONSUMES

[Pcd]
  gArmPlatformTokenSpaceGuid.PcdPL031RtcBase

[Depex]
  gFdtClientProtocolGuid
