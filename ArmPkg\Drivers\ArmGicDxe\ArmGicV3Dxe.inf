#/** @file
#
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2012 - 2017, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2025, Google LLC. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 1.30
  BASE_NAME                      = ArmGicV3Dxe
  FILE_GUID                      = 953ff472-9b9e-4058-84cf-227daf89dc82
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = GicV3DxeInitialize

[Sources.common]
  ArmGicCommonDxe.c
  ArmGicDxe.c
  ArmGicDxe.h
  GicV3/ArmGicV3Dxe.c

[Sources.ARM]
  GicV3/Arm/ArmGicV3.S     | GCC

[Sources.AARCH64]
  GicV3/AArch64/ArmGicV3.S

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
  DebugLib
  IoLib
  PcdLib
  PrintLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[Protocols]
  gHardwareInterruptProtocolGuid  ## PRODUCES
  gHardwareInterrupt2ProtocolGuid ## PRODUCES
  gEfiCpuArchProtocolGuid         ## CONSUMES

[Pcd.common]
  gArmTokenSpaceGuid.PcdGicDistributorBase
  gArmTokenSpaceGuid.PcdGicRedistributorsBase

[Depex]
  gEfiCpuArchProtocolGuid
