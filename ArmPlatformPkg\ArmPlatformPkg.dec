#/** @file
#
#  Copyright (c) 2011-2021, ARM Limited. All rights reserved.
#  Copyright (c) 2015, Intel Corporation. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = ArmPlatformPkg
  PACKAGE_GUID                   = 3308e0a0-1d94-11e0-915c-0002a5d5c51b
  PACKAGE_VERSION                = 0.1

################################################################################
#
# Include Section - list of Include Paths that are provided by this package.
#                   Comments are used for Keywords and Module Types.
#
# Supported Module Types:
#  BASE SEC PEI_CORE PEIM DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SMM_DRIVER DXE_SAL_DRIVER UEFI_DRIVER UEFI_APPLICATION
#
################################################################################
[Includes.common]
  Include                        # Root include for the package

[LibraryClasses]
  ##  @libraryclass  Provides an interface to query platform information.
  #
  ArmPlatformLib|Include/Library/ArmPlatformLib.h

  ##  @libraryclass  Provides an interface to initialize/shutdown a LCD screen.
  #
  LcdHwLib|Include/Library/LcdHwLib.h

  ##  @libraryclass  Provides an interface to configure a LCD screen.
  #
  LcdPlatformLib|Include/Library/LcdPlatformLib.h

  ##  @libraryclass  Provides an interface to the clock of a PL011 device.
  #
  PL011UartClockLib|Include/Library/PL011UartClockLib.h

  ##  @libraryclass  Provides an interface to a PL011 uart.
  #
  PL011UartLib|Include/Library/PL011UartLib.h

[Guids.common]
  gArmPlatformTokenSpaceGuid   = { 0x9c0aaed4, 0x74c5, 0x4043, { 0xb4, 0x17, 0xa3, 0x22, 0x38, 0x14, 0xce, 0x76 } }

[PcdsFeatureFlag.common]
  # Disable the GOP controller on ExitBootServices(). By default the value is FALSE,
  # we assume the OS will handle the FrameBuffer from the UEFI GOP information.
  gArmPlatformTokenSpaceGuid.PcdGopDisableOnExitBootServices|FALSE|BOOLEAN|0x0000003D

[PcdsFixedAtBuild.common]
  gArmPlatformTokenSpaceGuid.PcdCoreCount|1|UINT32|0x00000039
  gArmPlatformTokenSpaceGuid.PcdClusterCount|1|UINT32|0x00000038

  # Stack for CPU Cores in Non Secure Mode
  gArmPlatformTokenSpaceGuid.PcdCPUCoresStackBase|0|UINT64|0x00000009
  gArmPlatformTokenSpaceGuid.PcdCPUCorePrimaryStackSize|0x10000|UINT32|0x00000037

  # Size of the region used by UEFI in permanent memory (Reserved 128MB by default)
  gArmPlatformTokenSpaceGuid.PcdSystemMemoryUefiRegionSize|0x08000000|UINT32|0x00000015

  #
  # ARM Primecells
  #

  ## SP805 Watchdog
  gArmPlatformTokenSpaceGuid.PcdSP805WatchdogBase|0x0|UINT32|0x00000023
  gArmPlatformTokenSpaceGuid.PcdSP805WatchdogClockFrequencyInHz|32000|UINT32|0x00000021
  gArmPlatformTokenSpaceGuid.PcdSP805WatchdogInterrupt|0|UINT32|0x0000002E

  ## PL011 UART
  gArmPlatformTokenSpaceGuid.PL011UartClkInHz|24000000|UINT32|0x0000001F
  gArmPlatformTokenSpaceGuid.PL011UartInteger|0|UINT32|0x00000020
  gArmPlatformTokenSpaceGuid.PL011UartFractional|0|UINT32|0x0000002D
  gArmPlatformTokenSpaceGuid.PL011UartInterrupt|0x00000000|UINT32|0x0000002F
  gArmPlatformTokenSpaceGuid.PL011UartRegOffsetVariant|0|UINT8|0x0000003E

  ## PL011 Serial Debug UART
  gArmPlatformTokenSpaceGuid.PcdSerialDbgRegisterBase|0x00000000|UINT64|0x00000030
  gArmPlatformTokenSpaceGuid.PcdSerialDbgUartBaudRate|0x00000000|UINT64|0x00000031
  gArmPlatformTokenSpaceGuid.PcdSerialDbgUartClkInHz|0x00000000|UINT32|0x00000032
  gArmPlatformTokenSpaceGuid.PcdSerialDbgInterrupt|0x00000000|UINT32|0x00000041

  ## PL061 GPIO
  gArmPlatformTokenSpaceGuid.PcdPL061GpioBase|0x0|UINT32|0x00000025

  ## PL111 Lcd & HdLcd
  gArmPlatformTokenSpaceGuid.PcdPL111LcdBase|0x0|UINT32|0x00000026
  gArmPlatformTokenSpaceGuid.PcdArmHdLcdBase|0x0|UINT32|0x00000027

  ## Default size for display modes upto 1920x1080 (1920 * 1080 * 4 Bytes Per Pixel)
  gArmPlatformTokenSpaceGuid.PcdArmLcdDdrFrameBufferSize|0x7E9000|UINT32|0x00000043
  ## If set, framebuffer memory will be reserved and mapped in the system RAM
  gArmPlatformTokenSpaceGuid.PcdArmLcdDdrFrameBufferBase|0x0|UINT64|0x00000044

  ## ARM Mali Display Processor DP500/DP550/DP650
  gArmPlatformTokenSpaceGuid.PcdArmMaliDpBase|0x0|UINT64|0x00000050
  gArmPlatformTokenSpaceGuid.PcdArmMaliDpMemoryRegionLength|0x0|UINT32|0x00000051

  # Graphics Output Pixel format
  # 0 : PixelRedGreenBlueReserved8BitPerColor
  # 1 : PixelBlueGreenRedReserved8BitPerColor
  # 2 : PixelBitMask
  # Default is set to UEFI console font format PixelBlueGreenRedReserved8BitPerColor
  gArmPlatformTokenSpaceGuid.PcdGopPixelFormat|0x00000001|UINT32|0x00000040

  ## If set, this will swap settings for HDLCD RED_SELECT and BLUE_SELECT registers
  gArmPlatformTokenSpaceGuid.PcdArmHdLcdSwapBlueRedSelect|FALSE|BOOLEAN|0x00000045

[PcdsFixedAtBuild.common,PcdsDynamic.common]
  ## PL031 RealTimeClock
  gArmPlatformTokenSpaceGuid.PcdPL031RtcBase|0x0|UINT32|0x00000024
  gArmPlatformTokenSpaceGuid.PcdPL031RtcPpmAccuracy|300000000|UINT32|0x00000022

  gArmPlatformTokenSpaceGuid.PcdWatchdogCount|0x0|UINT32|0x00000033
