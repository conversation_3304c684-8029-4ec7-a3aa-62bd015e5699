/** @file

  Copyright (c) 2017-2018, Arm Limited. All rights reserved.

  SPDX-License-Identifier: BSD-2-Clause-Patent

  System Control and Management Interface V1.0
    http://infocenter.arm.com/help/topic/com.arm.doc.den0056a/
    DEN0056A_System_Control_and_Management_Interface.pdf
**/

#ifndef ARM_SCMI_H_
#define ARM_SCMI_H_

/* As per SCMI specification, maximum allowed ASCII string length
   for various return values/parameters of a SCMI message.
*/
#define SCMI_MAX_STR_LEN  16

#endif /* ARM_SCMI_H_ */
