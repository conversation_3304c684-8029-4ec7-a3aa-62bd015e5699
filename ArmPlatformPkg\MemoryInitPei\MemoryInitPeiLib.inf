#/** @file
#
#  Copyright (c) 2011-2014, ARM Ltd. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmMemoryInitPeiLib
  FILE_GUID                      = 55ddb6e0-70b5-11e0-b33e-0002a5d5c51b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = MemoryInitPeiLib|SEC PEIM

[Sources]
  MemoryInitPeiLib.c


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  DebugLib
  HobLib
  ArmMmuLib
  ArmPlatformLib

[Guids]
  gEfiMemoryTypeInformationGuid

[FeaturePcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiProduceMemoryTypeInformationHob

[FixedPcd]
  gArmTokenSpaceGuid.PcdFdBaseAddress
  gArmTokenSpaceGuid.PcdFdSize

  gArmPlatformTokenSpaceGuid.PcdSystemMemoryUefiRegionSize

  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode

[Pcd]
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmTokenSpaceGuid.PcdSystemMemorySize

[Depex]
  TRUE
