#/** @file
#
#  Copyright (c) 2018, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformPei
  FILE_GUID                      = 2ad0fc59-2314-4bf3-8633-13fa22a624a0
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InitializePlatformPeim

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC ARM
#

[Sources]
  PlatformPeim.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  ArmPlatformLib
  PlatformPeiLib
  PeiServicesLib

[Ppis]
  gEfiPeiMasterBootModePpiGuid                  # PPI ALWAYS_PRODUCED
  gEfiPeiBootInRecoveryModePpiGuid              # PPI SOMETIMES_PRODUCED

[FixedPcd]
  gArmTokenSpaceGuid.PcdFdBaseAddress
  gArmTokenSpaceGuid.PcdFdSize

  gArmTokenSpaceGuid.PcdFvBaseAddress
  gArmTokenSpaceGuid.PcdFvSize

[Depex]
  TRUE

