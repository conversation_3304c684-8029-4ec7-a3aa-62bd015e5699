#/** @file
#
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmPlatformPeiLib
  FILE_GUID                      = 49d37060-70b5-11e0-aa2d-0002a5d5c51b
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformPeiLib

[Sources]
  PlatformPeiLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  DebugLib
  HobLib
  ArmPlatformLib

[Ppis]
  gEfiPeiMasterBootModePpiGuid                  # PPI ALWAYS_PRODUCED
  gEfiPeiBootInRecoveryModePpiGuid              # PPI SOMETIMES_PRODUCED

[FixedPcd]
  gArmTokenSpaceGuid.PcdFdBaseAddress
  gArmTokenSpaceGuid.PcdFdSize

  gArmTokenSpaceGuid.PcdFvBaseAddress
  gArmTokenSpaceGuid.PcdFvSize

[depex]
  TRUE
