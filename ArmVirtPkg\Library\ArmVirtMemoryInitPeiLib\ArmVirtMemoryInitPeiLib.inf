#/** @file
#
#  Copyright (c) 2011-2014, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2014, Linaro Ltd. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmVirtMemoryInitPeiLib
  FILE_GUID                      = 021b6156-3cc8-4e99-85ee-13d8a871edf2
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = MemoryInitPeiLib

[Sources]
  ArmVirtMemoryInitPeiLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec

[LibraryClasses]
  DebugLib
  HobLib
  ArmLib
  ArmMmuLib
  ArmVirtMemInfoLib
  CacheMaintenanceLib

[Guids]
  gArmVirtSystemMemorySizeGuid
  gEfiMemoryTypeInformationGuid

[FeaturePcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiProduceMemoryTypeInformationHob

[FixedPcd]
  gArmTokenSpaceGuid.PcdFdSize

  gArmPlatformTokenSpaceGuid.PcdSystemMemoryUefiRegionSize

  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiBootServicesCode
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiBootServicesData
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiLoaderCode
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiLoaderData

[Pcd]
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmTokenSpaceGuid.PcdSystemMemorySize
  gArmTokenSpaceGuid.PcdFdBaseAddress

[Depex]
  TRUE
