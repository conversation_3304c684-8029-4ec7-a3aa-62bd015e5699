#/** @file
#
#  Copyright (c) 2008, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2011 - 2013, ARM Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DefaultExceptionHandlerLib
  FILE_GUID                      = EACDB354-DF1A-4AF9-A171-499737ED818F
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DefaultExceptionHandlerLib|DXE_CORE DXE_DRIVER

[Sources.common]
  DefaultExceptionHandlerUefi.c

[Sources.ARM]
  Arm/DefaultExceptionHandler.c

[Sources.AARCH64]
  AArch64/DefaultExceptionHandler.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  UefiLib
  BaseLib
  PrintLib
  DebugLib
  PeCoffGetEntryPointLib
  SerialPortLib
  UefiBootServicesTableLib

[Guids]
  gEfiDebugImageInfoTableGuid
