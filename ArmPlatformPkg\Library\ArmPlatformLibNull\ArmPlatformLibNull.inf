#/* @file
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#*/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmPlatformLibNull
  FILE_GUID                      = cb494bad-23ff-427e-8608-d7e138d3363b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmPlatformLib

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  ArmLib
  DebugLib

[Sources.common]
  ArmPlatformLibNull.c
  ArmPlatformLibNullMem.c

[Sources.Arm]
  Arm/ArmPlatformHelper.S    | GCC

[Sources.AArch64]
  AArch64/ArmPlatformHelper.S

[FixedPcd]
  gArmTokenSpaceGuid.PcdArmPrimaryCoreMask
  gArmTokenSpaceGuid.PcdArmPrimaryCore

[Ppis]
  gArmMpCoreInfoPpiGuid
