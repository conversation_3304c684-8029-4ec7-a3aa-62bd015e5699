#/** @file
#
#  Component description file for HDLCD module
#
#  Copyright (c) 2011-2018, ARM Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = HdLcd
  FILE_GUID                      = ce660500-824d-11e0-ac72-0002a5d5c51b
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = LcdHwLib

[Sources.common]
  HdLcd.h
  HdLcd.c

[Packages]
  ArmPlatformPkg/ArmPlatformPkg.dec
  ArmPkg/ArmPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  UefiLib
  BaseLib
  DebugLib
  IoLib

[FixedPcd]
  gArmPlatformTokenSpaceGuid.PcdArmHdLcdBase
  gArmPlatformTokenSpaceGuid.PcdArmHdLcdSwapBlueRedSelect

