## @file
#  Implementation for PlatformBootManagerLib library class interfaces.
#
#  Copyright (C) 2015-2016, Red Hat, Inc.
#  Copyright (c) 2014, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2007 - 2014, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2016, Linaro Ltd. All rights reserved.<BR>
#  Copyright (c) 2020 - 2021, Ampere Computing LLC. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = LinuxBootBootManagerLib
  FILE_GUID                      = 1FA91547-DB23-4F6A-8AF8-3B9782A7F917
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformBootManagerLib|DXE_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM AARCH64
#

[Sources]
  LinuxBootBm.c

[Packages]
  ArmPkg/ArmPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib
  PcdLib
  PrintLib
  UefiBootManagerLib
  UefiBootServicesTableLib
  UefiLib
  UefiRuntimeServicesTableLib

[Pcd]
  gArmTokenSpaceGuid.PcdLinuxBootFileGuid

[Guids]
  gEfiEndOfDxeEventGroupGuid
  gUefiShellFileGuid
  gZeroGuid

[Protocols]
  gEfiLoadedImageProtocolGuid
