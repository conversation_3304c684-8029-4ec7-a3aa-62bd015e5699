## @file
#  Nor Flash library for Kvmtool.
#
#  Copyright (c) 2020 - 2023, Arm Ltd. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = NorFlashKvmtoolLib
  FILE_GUID                      = E75F07A1-B160-4893-BDD4-09E32FF847DC
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = VirtNorFlashPlatformLib
  CONSTRUCTOR                    = NorFlashPlatformLibConstructor

[Sources.common]
  NorFlashKvmtool.c

[Packages]
  ArmPkg/ArmPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  OvmfPkg/OvmfPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  PcdLib
  UefiBootServicesTableLib

[Protocols]
  gFdtClientProtocolGuid          ## CONSUMES

[Pcd]
  gArmTokenSpaceGuid.PcdFvBaseAddress
  gArmTokenSpaceGuid.PcdFvSize

  gEfiMdeModulePkgTokenSpaceGuid.PcdEmuVariableNvModeEnable
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableBase
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableSize
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwWorkingBase
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwWorkingSize
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwSpareBase
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwSpareSize

[Depex]
  gFdtClientProtocolGuid
