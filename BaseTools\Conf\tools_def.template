#
#  Copyright (c) 2006 - 2025, Intel Corporation. All rights reserved.<BR>
#  Portions copyright (c) 2008 - 2009, Apple Inc. All rights reserved.<BR>
#  Portions copyright (c) 2011 - 2019, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2015, Hewlett-Packard Development Company, L.P.<BR>
#  (C) Copyright 2020, Hewlett Packard Enterprise Development LP<BR>
#  Copyright (c) 2022, Loongson Technology Corporation Limited. All rights reserved.<BR>
#  Copyright (c) Microsoft Corporation
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
# Increase this version tag any time you want user to get warning about updating this
# file in the Conf dir.  By default it does not do update existing conf dirs.
#
# 2.00 - Initial version with changes for CI
#      - Change RC path to use plugin
# 3.00 - Update toolchains
#      - Add support for ARM and AARCH64 to CLANGDWARF
#      - Remove VS2008, VS2010, VS2012, VS2013, CLANG35, CLANG38, EBC
#      - Add GCC and GCCNOLTO
#      - Deprecate GCC48, GCC49 and GCC5.
# 3.01 - Add toolchain for VS2022
# 3.02 - Enable stack cookies for IA32, X64, ARM, and AARCH64 builds for GCC and MSVC
#
#!VERSION=3.02

IDENTIFIER = Default TOOL_CHAIN_CONF

# common path macros
DEFINE VS2015_BIN      = ENV(VS2015_PREFIX)Vc\bin
DEFINE VS2015_DLL      = ENV(VS2015_PREFIX)Common7\IDE;DEF(VS2015_BIN)
DEFINE VS2015_BINX64   = DEF(VS2015_BIN)\x86_amd64

DEFINE VS2015x86_BIN    = ENV(VS2015_PREFIX)Vc\bin
DEFINE VS2015x86_DLL    = ENV(VS2015_PREFIX)Common7\IDE;DEF(VS2015x86_BIN)
DEFINE VS2015x86_BINX64 = DEF(VS2015x86_BIN)\x86_amd64

DEFINE VS_HOST            = x86

DEFINE VS2017_BIN         = ENV(VS2017_PREFIX)bin
DEFINE VS2017_BIN_HOST    = DEF(VS2017_BIN)\HostDEF(VS_HOST)\DEF(VS_HOST)
DEFINE VS2017_BIN_IA32    = DEF(VS2017_BIN)\HostDEF(VS_HOST)\x86
DEFINE VS2017_BIN_X64     = DEF(VS2017_BIN)\HostDEF(VS_HOST)\x64
DEFINE VS2017_BIN_ARM     = DEF(VS2017_BIN)\HostDEF(VS_HOST)\arm
DEFINE VS2017_BIN_AARCH64 = DEF(VS2017_BIN)\HostDEF(VS_HOST)\arm64

DEFINE VS2019_BIN         = ENV(VS2019_PREFIX)bin
DEFINE VS2019_BIN_HOST    = DEF(VS2019_BIN)\HostDEF(VS_HOST)\DEF(VS_HOST)
DEFINE VS2019_BIN_IA32    = DEF(VS2019_BIN)\HostDEF(VS_HOST)\x86
DEFINE VS2019_BIN_X64     = DEF(VS2019_BIN)\HostDEF(VS_HOST)\x64
DEFINE VS2019_BIN_ARM     = DEF(VS2019_BIN)\HostDEF(VS_HOST)\arm
DEFINE VS2019_BIN_AARCH64 = DEF(VS2019_BIN)\HostDEF(VS_HOST)\arm64

DEFINE VS2022_BIN         = ENV(VS2022_PREFIX)bin
DEFINE VS2022_BIN_HOST    = DEF(VS2022_BIN)\HostDEF(VS_HOST)\DEF(VS_HOST)
DEFINE VS2022_BIN_IA32    = DEF(VS2022_BIN)\HostDEF(VS_HOST)\x86
DEFINE VS2022_BIN_X64     = DEF(VS2022_BIN)\HostDEF(VS_HOST)\x64
DEFINE VS2022_BIN_ARM     = DEF(VS2022_BIN)\HostDEF(VS_HOST)\arm
DEFINE VS2022_BIN_AARCH64 = DEF(VS2022_BIN)\HostDEF(VS_HOST)\arm64

#
# Resource compiler
#
DEFINE RC_PATH    = ENV(WINSDK_PATH_FOR_RC_EXE)\rc.exe

DEFINE WINSDK_BIN           = ENV(WINSDK_PREFIX)
DEFINE WINSDKx86_BIN        = ENV(WINSDKx86_PREFIX)

# Microsoft Visual Studio 2015 Professional Edition
DEFINE WINSDK81_BIN         = ENV(WINSDK81_PREFIX)x64
DEFINE WINSDK81x86_BIN      = ENV(WINSDK81x86_PREFIX)x86

# Microsoft Visual Studio 2017/2019/2022 Professional Edition
DEFINE WINSDK10_BIN         = ENV(WINSDK10_PREFIX)DEF(VS_HOST)

# These defines are needed for certain Microsoft Visual Studio tools that
# are used by other toolchains.  An example is that ICC on Windows normally
# uses Microsoft's nmake.exe.

DEFINE WINDDK_BIN16     = ENV(WINDDK3790_PREFIX)bin16
DEFINE WINDDK_BINX64    = ENV(WINDDK3790_PREFIX)win64\x86\amd64

DEFINE CYGWIN_BIN              = c:/cygwin/bin
DEFINE CYGWIN_BINIA32          = c:/cygwin/opt/tiano/i386-tiano-pe/i386-tiano-pe/bin/
DEFINE CYGWIN_BINX64           = c:/cygwin/opt/tiano/x86_64-pc-mingw64/x86_64-pc-mingw64/bin/

DEFINE GCC48_IA32_PREFIX       = ENV(GCC48_BIN)
DEFINE GCC48_X64_PREFIX        = ENV(GCC48_BIN)

DEFINE GCC49_IA32_PREFIX       = ENV(GCC49_BIN)
DEFINE GCC49_X64_PREFIX        = ENV(GCC49_BIN)

DEFINE GCCNOLTO_IA32_PREFIX    = ENV(GCCNOLTO_BIN)
DEFINE GCCNOLTO_X64_PREFIX     = ENV(GCCNOLTO_BIN)

DEFINE GCC5_IA32_PREFIX        = ENV(GCC5_BIN)
DEFINE GCC5_X64_PREFIX         = ENV(GCC5_BIN)
DEFINE GCC_IA32_PREFIX         = ENV(GCC_BIN)
DEFINE GCC_X64_PREFIX          = ENV(GCC_BIN)
DEFINE GCC_HOST_PREFIX         = ENV(GCC_HOST_BIN)

DEFINE UNIX_IASL_BIN           = ENV(IASL_PREFIX)iasl
DEFINE WIN_IASL_BIN            = ENV(IASL_PREFIX)iasl.exe

DEFINE IASL_FLAGS              =
DEFINE IASL_OUTFLAGS           = -p

DEFINE DEFAULT_WIN_ASL_BIN      = DEF(WIN_IASL_BIN)
DEFINE DEFAULT_WIN_ASL_FLAGS    = DEF(IASL_FLAGS)
DEFINE DEFAULT_WIN_ASL_OUTFLAGS = DEF(IASL_OUTFLAGS)

# MSFT Build Flag for included header file list generation
DEFINE MSFT_DEPS_FLAGS         = /showIncludes

DEFINE MSFT_ASLPP_FLAGS        = /nologo /E /C /FIAutoGen.h
DEFINE MSFT_ASLCC_FLAGS        = /nologo /c /FIAutoGen.h /TC /Dmain=ReferenceAcpiTable
DEFINE MSFT_ASLDLINK_FLAGS     = /NODEFAULTLIB /ENTRY:ReferenceAcpiTable /SUBSYSTEM:CONSOLE

DEFINE DTCPP_BIN               = ENV(DTCPP_PREFIX)cpp
DEFINE DTC_BIN                 = ENV(DTC_PREFIX)dtc

####################################################################################
#
# format: TARGET_TOOLCHAIN_ARCH_COMMANDTYPE_ATTRIBUTE = <string>
# priority:
#         TARGET_TOOLCHAIN_ARCH_COMMANDTYPE_ATTRIBUTE (Highest)
#         ******_TOOLCHAIN_ARCH_COMMANDTYPE_ATTRIBUTE
#         TARGET_*********_ARCH_COMMANDTYPE_ATTRIBUTE
#         ******_*********_ARCH_COMMANDTYPE_ATTRIBUTE
#         TARGET_TOOLCHAIN_****_COMMANDTYPE_ATTRIBUTE
#         ******_TOOLCHAIN_****_COMMANDTYPE_ATTRIBUTE
#         TARGET_*********_****_COMMANDTYPE_ATTRIBUTE
#         ******_*********_****_COMMANDTYPE_ATTRIBUTE
#         TARGET_TOOLCHAIN_ARCH_***********_ATTRIBUTE
#         ******_TOOLCHAIN_ARCH_***********_ATTRIBUTE
#         TARGET_*********_ARCH_***********_ATTRIBUTE
#         ******_*********_ARCH_***********_ATTRIBUTE
#         TARGET_TOOLCHAIN_****_***********_ATTRIBUTE
#         ******_TOOLCHAIN_****_***********_ATTRIBUTE
#         TARGET_*********_****_***********_ATTRIBUTE
#         ******_*********_****_***********_ATTRIBUTE (Lowest)
#
####################################################################################
####################################################################################
#
# Supported Tool Chains
# =====================
#   VS2015      -win32-  Requires:
#                             Microsoft Visual Studio 2015 Professional Edition, Update 3
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler (iasl.exe) from
#                               https://acpica.org/downloads
#   VS2017      -win32-  Requires:
#                             Microsoft Visual Studio 2017 version 15.2 (15.4 for ARM64) or later
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler (iasl.exe) from
#                               https://acpica.org/downloads
#                        Note:
#                             Building of XIP firmware images for ARM/ARM64 is not currently supported (only applications).
#                             /FILEALIGN:4096 and other changes are needed for ARM firmware builds.
#   VS2019      -win32-  Requires:
#                             Microsoft Visual Studio 2019 version 16.2 or later
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler (iasl.exe) from
#                               https://acpica.org/downloads
#   VS2022      -win32,win64-  Requires:
#                             Microsoft Visual Studio 2022 version 17.0 or later
#                        Optional:
#                             Required to build EBC drivers:
#                               Intel(r) Compiler for Efi Byte Code (Intel(r) EBC Compiler)
#   GCCNOLTO    -Linux,Windows-  Requires:
#                             GCC 4.9 targeting x86_64-linux-gnu, aarch64-linux-gnu, or arm-linux-gnueabi
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler from
#                               https://acpica.org/downloads
#   GCC         -Linux,Windows-  Requires:
#                             GCC 5 with LTO support, targeting x86_64-linux-gnu, aarch64-linux-gnu, arm-linux-gnueabi, riscv64-linux-gnu or loongarch64-linux-gnu
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler from
#                               https://acpica.org/downloads
#
#   CLANGPDB -Linux, Windows, Mac-  Requires:
#                             Clang 9 or above from http://releases.llvm.org/
#                        Optional:
#                             Required to compile nasm source:
#                               nasm compiler from
#                               NASM -- http://www.nasm.us/
#   CLANGDWARF -Linux, Windows, Mac-  Requires:
#                             Clang 9 or above from http://releases.llvm.org/
#                        Optional:
#                             Required to compile nasm source:
#                               nasm compiler from
#                               NASM -- http://www.nasm.us/
#   VS2015x86   -win64-  Requires:
#                             Microsoft Visual Studio 2015 (x86) Update 3 or above
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler (iasl.exe) from
#                               https://acpica.org/downloads
#
# Deprecated Tool Chains
# ======================
#   GCC48       -Linux,Windows-  Requires:
#                             GCC 4.8 targeting x86_64-linux-gnu, aarch64-linux-gnu, or arm-linux-gnueabi
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler from
#                               https://acpica.org/downloads
#   GCC49       -Linux,Windows-  Requires:
#                             GCC 4.9 targeting x86_64-linux-gnu, aarch64-linux-gnu, or arm-linux-gnueabi
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler from
#                               https://acpica.org/downloads
#   GCC5        -Linux,Windows-  Requires:
#                             GCC 5 with LTO support, targeting x86_64-linux-gnu, aarch64-linux-gnu, arm-linux-gnueabi, riscv64-linux-gnu or loongarch64-linux-gnu
#                        Optional:
#                             Required to build platforms or ACPI tables:
#                               Intel(r) ACPI Compiler from
#                               https://acpica.org/downloads
#
####################################################################################
####################################################################################
#
# Supported Tool Chain Family
# ===========================
#   MSFT           - Microsoft
#   GCC            - GNU GCC
#   INTEL          - INTEL
####################################################################################
####################################################################################
#
# Other Supported Tools
# =====================
#   NASM -- http://www.nasm.us/
#   - NASM 2.15.05 or later for use with the GCC toolchain family
#   - NASM 2.15.05 or later for use with all other toolchain families
#
####################################################################################
####################################################################################
#
# Intel ACPI Source Language Compiler (Template)
#
####################################################################################
# *_*_*_ASL_FAMILY                   = INTEL
#
# *_*_*_ASL_PATH                     = C:\ASL\iasl.exe
#
####################################################################################
#
# Microsoft ACPI Source Language Compiler (Template)
#
####################################################################################
# *_*_*_ASL_FAMILY                   = MSFT
#
# *_*_*_ASL_PATH                     = C:\ASL\asl.exe
#
####################################################################################

####################################################################################
#
# Microsoft Visual Studio 2015
#
#   VS2015  - Microsoft Visual Studio 2015 Professional Edition with Intel ASL
#   ASL     - Intel ACPI Source Language Compiler
####################################################################################
#   VS2015           - Microsoft Visual Studio 2015 Professional Edition
*_VS2015_*_*_FAMILY               = MSFT

*_VS2015_*_MAKE_PATH              = DEF(VS2015_BIN)\nmake.exe
*_VS2015_*_MAKE_FLAGS             = /nologo
*_VS2015_*_RC_PATH                = DEF(WINSDK81_BIN)\rc.exe

*_VS2015_*_SLINK_FLAGS            = /NOLOGO /LTCG
*_VS2015_*_APP_FLAGS              = /nologo /E /TC
*_VS2015_*_PP_FLAGS               = /nologo /E /TC /FIAutoGen.h
*_VS2015_*_VFRPP_FLAGS            = /nologo /E /TC /DVFRCOMPILE /FI$(MODULE_NAME)StrDefs.h
*_VS2015_*_DLINK2_FLAGS           =
*_VS2015_*_DEPS_FLAGS      = DEF(MSFT_DEPS_FLAGS)
*_VS2015_*_ASM16_PATH             = DEF(VS2015_BIN)\ml.exe

##################
# ASL definitions
##################
*_VS2015_*_ASL_PATH               = DEF(DEFAULT_WIN_ASL_BIN)
*_VS2015_*_ASL_FLAGS              = DEF(DEFAULT_WIN_ASL_FLAGS)
*_VS2015_*_ASL_OUTFLAGS           = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_VS2015_*_ASLCC_FLAGS            = DEF(MSFT_ASLCC_FLAGS)
*_VS2015_*_ASLPP_FLAGS            = DEF(MSFT_ASLPP_FLAGS)
*_VS2015_*_ASLDLINK_FLAGS         = DEF(MSFT_ASLDLINK_FLAGS)

##################
# IA32 definitions
##################
*_VS2015_IA32_*_DLL               = DEF(VS2015_DLL)

*_VS2015_IA32_CC_PATH             = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_VFRPP_PATH          = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_SLINK_PATH          = DEF(VS2015_BIN)\lib.exe
*_VS2015_IA32_DLINK_PATH          = DEF(VS2015_BIN)\link.exe
*_VS2015_IA32_APP_PATH            = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_PP_PATH             = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_ASM_PATH            = DEF(VS2015_BIN)\ml.exe
*_VS2015_IA32_ASLCC_PATH          = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_ASLPP_PATH          = DEF(VS2015_BIN)\cl.exe
*_VS2015_IA32_ASLDLINK_PATH       = DEF(VS2015_BIN)\link.exe

  DEBUG_VS2015_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Gw
RELEASE_VS2015_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2015_IA32_CC_FLAGS        = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Od

  DEBUG_VS2015_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd /Zi
RELEASE_VS2015_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd
NOOPT_VS2015_IA32_ASM_FLAGS       = /nologo /c /WX /W3 /Cx /coff /Zd /Zi

  DEBUG_VS2015_IA32_NASM_FLAGS    = -Ox -f win32 -g
RELEASE_VS2015_IA32_NASM_FLAGS    = -Ox -f win32
NOOPT_VS2015_IA32_NASM_FLAGS      = -O0 -f win32 -g

  DEBUG_VS2015_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2015_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2015_IA32_DLINK_FLAGS     = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

##################
# X64 definitions
##################
*_VS2015_X64_*_DLL         = DEF(VS2015_DLL)

*_VS2015_X64_CC_PATH       = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_PP_PATH       = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_APP_PATH      = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_VFRPP_PATH    = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_ASM_PATH      = DEF(VS2015_BINX64)\ml64.exe
*_VS2015_X64_SLINK_PATH    = DEF(VS2015_BINX64)\lib.exe
*_VS2015_X64_DLINK_PATH    = DEF(VS2015_BINX64)\link.exe
*_VS2015_X64_ASLCC_PATH    = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_ASLPP_PATH    = DEF(VS2015_BINX64)\cl.exe
*_VS2015_X64_ASLDLINK_PATH = DEF(VS2015_BINX64)\link.exe

  DEBUG_VS2015_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Gw
RELEASE_VS2015_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2015_X64_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Od

  DEBUG_VS2015_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi
RELEASE_VS2015_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd
NOOPT_VS2015_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi

  DEBUG_VS2015_X64_NASM_FLAGS   = -Ox -f win64 -g
RELEASE_VS2015_X64_NASM_FLAGS   = -Ox -f win64
NOOPT_VS2015_X64_NASM_FLAGS     = -O0 -f win64 -g

  DEBUG_VS2015_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2015_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2015_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

####################################################################################
#   VS2015x86       - Microsoft Visual Studio 2015 (x86) professional with Intel ASL
#   ASL  - Intel ACPI Source Language Compiler (iasl.exe)
####################################################################################
#   VS2015x86           - Microsoft Visual Studio 2015 (x86) professional Edition with Intel ASL
*_VS2015x86_*_*_FAMILY        = MSFT

*_VS2015x86_*_MAKE_PATH       = DEF(VS2015x86_BIN)\nmake.exe
*_VS2015x86_*_MAKE_FLAGS      = /nologo
*_VS2015x86_*_RC_PATH         = DEF(WINSDK81x86_BIN)\rc.exe

*_VS2015x86_*_SLINK_FLAGS     = /NOLOGO /LTCG
*_VS2015x86_*_APP_FLAGS       = /nologo /E /TC
*_VS2015x86_*_PP_FLAGS        = /nologo /E /TC /FIAutoGen.h
*_VS2015x86_*_VFRPP_FLAGS     = /nologo /E /TC /DVFRCOMPILE /FI$(MODULE_NAME)StrDefs.h
*_VS2015x86_*_DLINK2_FLAGS    =
*_VS2015x86_*_DEPS_FLAGS      = DEF(MSFT_DEPS_FLAGS)
*_VS2015x86_*_ASM16_PATH      = DEF(VS2015x86_BIN)\ml.exe

##################
# ASL definitions
##################
*_VS2015x86_*_ASL_PATH        = DEF(WIN_IASL_BIN)
*_VS2015x86_*_ASL_FLAGS       = DEF(DEFAULT_WIN_ASL_FLAGS)
*_VS2015x86_*_ASL_OUTFLAGS    = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_VS2015x86_*_ASLCC_FLAGS     = DEF(MSFT_ASLCC_FLAGS)
*_VS2015x86_*_ASLPP_FLAGS     = DEF(MSFT_ASLPP_FLAGS)
*_VS2015x86_*_ASLDLINK_FLAGS  = DEF(MSFT_ASLDLINK_FLAGS)

##################
# IA32 definitions
##################
*_VS2015x86_IA32_*_DLL        = DEF(VS2015x86_DLL)

*_VS2015x86_IA32_CC_PATH      = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_VFRPP_PATH   = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_ASLCC_PATH   = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_ASLPP_PATH   = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_SLINK_PATH   = DEF(VS2015x86_BIN)\lib.exe
*_VS2015x86_IA32_DLINK_PATH   = DEF(VS2015x86_BIN)\link.exe
*_VS2015x86_IA32_ASLDLINK_PATH= DEF(VS2015x86_BIN)\link.exe
*_VS2015x86_IA32_APP_PATH     = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_PP_PATH      = DEF(VS2015x86_BIN)\cl.exe
*_VS2015x86_IA32_ASM_PATH     = DEF(VS2015x86_BIN)\ml.exe

  DEBUG_VS2015x86_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Gw
RELEASE_VS2015x86_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2015x86_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Od

  DEBUG_VS2015x86_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd /Zi
RELEASE_VS2015x86_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd
NOOPT_VS2015x86_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd /Zi

  DEBUG_VS2015x86_IA32_NASM_FLAGS  = -Ox -f win32 -g
RELEASE_VS2015x86_IA32_NASM_FLAGS  = -Ox -f win32
NOOPT_VS2015x86_IA32_NASM_FLAGS    = -O0 -f win32 -g

  DEBUG_VS2015x86_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2015x86_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2015x86_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

##################
# X64 definitions
##################
*_VS2015x86_X64_*_DLL         = DEF(VS2015x86_DLL)

*_VS2015x86_X64_CC_PATH       = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_PP_PATH       = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_APP_PATH      = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_VFRPP_PATH    = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_ASLCC_PATH    = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_ASLPP_PATH    = DEF(VS2015x86_BINX64)\cl.exe
*_VS2015x86_X64_ASM_PATH      = DEF(VS2015x86_BINX64)\ml64.exe
*_VS2015x86_X64_SLINK_PATH    = DEF(VS2015x86_BINX64)\lib.exe
*_VS2015x86_X64_DLINK_PATH    = DEF(VS2015x86_BINX64)\link.exe
*_VS2015x86_X64_ASLDLINK_PATH = DEF(VS2015x86_BINX64)\link.exe

  DEBUG_VS2015x86_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Gw
RELEASE_VS2015x86_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2015x86_X64_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Od

  DEBUG_VS2015x86_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi
RELEASE_VS2015x86_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd
NOOPT_VS2015x86_X64_ASM_FLAGS      = /nologo /c /WX /W3 /Cx /Zd /Zi

  DEBUG_VS2015x86_X64_NASM_FLAGS   = -Ox -f win64 -g
RELEASE_VS2015x86_X64_NASM_FLAGS   = -Ox -f win64
NOOPT_VS2015x86_X64_NASM_FLAGS     = -O0 -f win64 -g

  DEBUG_VS2015x86_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2015x86_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2015x86_X64_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

####################################################################################
#   VS2017       - Microsoft Visual Studio 2017 with Intel ASL
#   ASL          - Intel ACPI Source Language Compiler (iasl.exe)
####################################################################################
#   VS2017           - Microsoft Visual Studio 2017 professional Edition with Intel ASL
*_VS2017_*_*_FAMILY        = MSFT
*_VS2017_*_*_DLL           = DEF(VS2017_BIN_HOST)

*_VS2017_*_MAKE_PATH       = DEF(VS2017_BIN_HOST)\nmake.exe
*_VS2017_*_MAKE_FLAGS      = /nologo
*_VS2017_*_RC_PATH         = DEF(RC_PATH)

*_VS2017_*_SLINK_FLAGS     = /NOLOGO /LTCG
*_VS2017_*_APP_FLAGS       = /nologo /E /TC
*_VS2017_*_PP_FLAGS        = /nologo /E /TC /FIAutoGen.h
*_VS2017_*_VFRPP_FLAGS     = /nologo /E /TC /DVFRCOMPILE /FI$(MODULE_NAME)StrDefs.h
*_VS2017_*_DLINK2_FLAGS    = /WHOLEARCHIVE
*_VS2017_*_ASM16_PATH      = DEF(VS2017_BIN_IA32)\ml.exe
*_VS2017_*_DEPS_FLAGS      = DEF(MSFT_DEPS_FLAGS)
##################
# ASL definitions
##################
*_VS2017_*_ASL_PATH        = DEF(WIN_IASL_BIN)
*_VS2017_*_ASL_FLAGS       = DEF(DEFAULT_WIN_ASL_FLAGS)
*_VS2017_*_ASL_OUTFLAGS    = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_VS2017_*_ASLCC_FLAGS     = DEF(MSFT_ASLCC_FLAGS)
*_VS2017_*_ASLPP_FLAGS     = DEF(MSFT_ASLPP_FLAGS)
*_VS2017_*_ASLDLINK_FLAGS  = DEF(MSFT_ASLDLINK_FLAGS)

##################
# IA32 definitions
##################
*_VS2017_IA32_CC_PATH      = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_VFRPP_PATH   = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_ASLCC_PATH   = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_ASLPP_PATH   = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_SLINK_PATH   = DEF(VS2017_BIN_IA32)\lib.exe
*_VS2017_IA32_DLINK_PATH   = DEF(VS2017_BIN_IA32)\link.exe
*_VS2017_IA32_ASLDLINK_PATH= DEF(VS2017_BIN_IA32)\link.exe
*_VS2017_IA32_APP_PATH     = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_PP_PATH      = DEF(VS2017_BIN_IA32)\cl.exe
*_VS2017_IA32_ASM_PATH     = DEF(VS2017_BIN_IA32)\ml.exe

  DEBUG_VS2017_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Gw
RELEASE_VS2017_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2017_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Od

  DEBUG_VS2017_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd /Zi
RELEASE_VS2017_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd
NOOPT_VS2017_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd /Zi

  DEBUG_VS2017_IA32_NASM_FLAGS  = -Ox -f win32 -g
RELEASE_VS2017_IA32_NASM_FLAGS  = -Ox -f win32
NOOPT_VS2017_IA32_NASM_FLAGS    = -O0 -f win32 -g

  DEBUG_VS2017_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2017_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2017_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

##################
# X64 definitions
##################
*_VS2017_X64_CC_PATH       = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_PP_PATH       = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_APP_PATH      = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_VFRPP_PATH    = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_ASLCC_PATH    = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_ASLPP_PATH    = DEF(VS2017_BIN_X64)\cl.exe
*_VS2017_X64_ASM_PATH      = DEF(VS2017_BIN_X64)\ml64.exe
*_VS2017_X64_SLINK_PATH    = DEF(VS2017_BIN_X64)\lib.exe
*_VS2017_X64_DLINK_PATH    = DEF(VS2017_BIN_X64)\link.exe
*_VS2017_X64_ASLDLINK_PATH = DEF(VS2017_BIN_X64)\link.exe

  DEBUG_VS2017_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Gw
RELEASE_VS2017_X64_CC_FLAGS     = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2017_X64_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Od

  DEBUG_VS2017_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi
RELEASE_VS2017_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd
NOOPT_VS2017_X64_ASM_FLAGS      = /nologo /c /WX /W3 /Cx /Zd /Zi

  DEBUG_VS2017_X64_NASM_FLAGS   = -Ox -f win64 -g
RELEASE_VS2017_X64_NASM_FLAGS   = -Ox -f win64
NOOPT_VS2017_X64_NASM_FLAGS     = -O0 -f win64 -g

  DEBUG_VS2017_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2017_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2017_X64_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

#################
# ARM definitions
#################
*_VS2017_ARM_CC_PATH              = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_VFRPP_PATH           = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_SLINK_PATH           = DEF(VS2017_BIN_ARM)\lib.exe
*_VS2017_ARM_DLINK_PATH           = DEF(VS2017_BIN_ARM)\link.exe
*_VS2017_ARM_APP_PATH             = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_PP_PATH              = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_ASM_PATH             = DEF(VS2017_BIN_ARM)\armasm.exe
*_VS2017_ARM_ASLCC_PATH           = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_ASLPP_PATH           = DEF(VS2017_BIN_ARM)\cl.exe
*_VS2017_ARM_ASLDLINK_PATH        = DEF(VS2017_BIN_ARM)\link.exe

  DEBUG_VS2017_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2017_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2017_ARM_CC_FLAGS         = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2017_ARM_ASM_FLAGS      = /nologo /g
RELEASE_VS2017_ARM_ASM_FLAGS      = /nologo
NOOPT_VS2017_ARM_ASM_FLAGS        = /nologo

  DEBUG_VS2017_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2017_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2017_ARM_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

#####################
# AARCH64 definitions
#####################
*_VS2017_AARCH64_CC_PATH           = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_VFRPP_PATH        = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_SLINK_PATH        = DEF(VS2017_BIN_AARCH64)\lib.exe
*_VS2017_AARCH64_DLINK_PATH        = DEF(VS2017_BIN_AARCH64)\link.exe
*_VS2017_AARCH64_APP_PATH          = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_PP_PATH           = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_ASM_PATH          = DEF(VS2017_BIN_AARCH64)\armasm64.exe
*_VS2017_AARCH64_ASLCC_PATH        = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_ASLPP_PATH        = DEF(VS2017_BIN_AARCH64)\cl.exe
*_VS2017_AARCH64_ASLDLINK_PATH     = DEF(VS2017_BIN_AARCH64)\link.exe

  DEBUG_VS2017_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2017_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2017_AARCH64_CC_FLAGS      = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2017_AARCH64_ASM_FLAGS   = /nologo /g
RELEASE_VS2017_AARCH64_ASM_FLAGS   = /nologo
NOOPT_VS2017_AARCH64_ASM_FLAGS     = /nologo

  DEBUG_VS2017_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG
RELEASE_VS2017_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /MERGE:.rdata=.data
NOOPT_VS2017_AARCH64_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG

####################################################################################
#   VS2019       - Microsoft Visual Studio 2019 with Intel ASL
#   ASL          - Intel ACPI Source Language Compiler (iasl.exe)
####################################################################################
#   VS2019           - Microsoft Visual Studio 2017 professional Edition with Intel ASL
*_VS2019_*_*_FAMILY        = MSFT
*_VS2019_*_*_DLL           = DEF(VS2019_BIN_HOST)

*_VS2019_*_MAKE_PATH       = DEF(VS2019_BIN_HOST)\nmake.exe
*_VS2019_*_MAKE_FLAGS      = /nologo
*_VS2019_*_RC_PATH         = DEF(RC_PATH)

*_VS2019_*_SLINK_FLAGS     = /NOLOGO /LTCG
*_VS2019_*_APP_FLAGS       = /nologo /E /TC
*_VS2019_*_PP_FLAGS        = /nologo /E /TC /FIAutoGen.h
*_VS2019_*_VFRPP_FLAGS     = /nologo /E /TC /DVFRCOMPILE /FI$(MODULE_NAME)StrDefs.h
*_VS2019_*_DLINK2_FLAGS    = /WHOLEARCHIVE
*_VS2019_*_ASM16_PATH      = DEF(VS2019_BIN_IA32)\ml.exe
*_VS2019_*_DEPS_FLAGS      = DEF(MSFT_DEPS_FLAGS)
##################
# ASL definitions
##################
*_VS2019_*_ASL_PATH        = DEF(WIN_IASL_BIN)
*_VS2019_*_ASL_FLAGS       = DEF(DEFAULT_WIN_ASL_FLAGS)
*_VS2019_*_ASL_OUTFLAGS    = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_VS2019_*_ASLCC_FLAGS     = DEF(MSFT_ASLCC_FLAGS)
*_VS2019_*_ASLPP_FLAGS     = DEF(MSFT_ASLPP_FLAGS)
*_VS2019_*_ASLDLINK_FLAGS  = DEF(MSFT_ASLDLINK_FLAGS)

##################
# IA32 definitions
##################
*_VS2019_IA32_CC_PATH      = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_VFRPP_PATH   = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_ASLCC_PATH   = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_ASLPP_PATH   = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_SLINK_PATH   = DEF(VS2019_BIN_IA32)\lib.exe
*_VS2019_IA32_DLINK_PATH   = DEF(VS2019_BIN_IA32)\link.exe
*_VS2019_IA32_ASLDLINK_PATH= DEF(VS2019_BIN_IA32)\link.exe
*_VS2019_IA32_APP_PATH     = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_PP_PATH      = DEF(VS2019_BIN_IA32)\cl.exe
*_VS2019_IA32_ASM_PATH     = DEF(VS2019_BIN_IA32)\ml.exe

  DEBUG_VS2019_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Gw
RELEASE_VS2019_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2019_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Od

  DEBUG_VS2019_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd /Zi
RELEASE_VS2019_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd
NOOPT_VS2019_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd /Zi

  DEBUG_VS2019_IA32_NASM_FLAGS  = -Ox -f win32 -g
RELEASE_VS2019_IA32_NASM_FLAGS  = -Ox -f win32
NOOPT_VS2019_IA32_NASM_FLAGS    = -O0 -f win32 -g

  DEBUG_VS2019_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2019_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2019_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

##################
# X64 definitions
##################
*_VS2019_X64_CC_PATH       = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_PP_PATH       = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_APP_PATH      = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_VFRPP_PATH    = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_ASLCC_PATH    = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_ASLPP_PATH    = DEF(VS2019_BIN_X64)\cl.exe
*_VS2019_X64_ASM_PATH      = DEF(VS2019_BIN_X64)\ml64.exe
*_VS2019_X64_SLINK_PATH    = DEF(VS2019_BIN_X64)\lib.exe
*_VS2019_X64_DLINK_PATH    = DEF(VS2019_BIN_X64)\link.exe
*_VS2019_X64_ASLDLINK_PATH = DEF(VS2019_BIN_X64)\link.exe

  DEBUG_VS2019_X64_CC_FLAGS     = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Gw /volatileMetadata-
RELEASE_VS2019_X64_CC_FLAGS     = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Gw /volatileMetadata-
NOOPT_VS2019_X64_CC_FLAGS       = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Od /volatileMetadata-

  DEBUG_VS2019_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi
RELEASE_VS2019_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd
NOOPT_VS2019_X64_ASM_FLAGS      = /nologo /c /WX /W3 /Cx /Zd /Zi

  DEBUG_VS2019_X64_NASM_FLAGS   = -Ox -f win64 -g
RELEASE_VS2019_X64_NASM_FLAGS   = -Ox -f win64
NOOPT_VS2019_X64_NASM_FLAGS     = -O0 -f win64 -g

  DEBUG_VS2019_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2019_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2019_X64_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

#################
# ARM definitions
#################
*_VS2019_ARM_CC_PATH              = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_VFRPP_PATH           = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_SLINK_PATH           = DEF(VS2019_BIN_ARM)\lib.exe
*_VS2019_ARM_DLINK_PATH           = DEF(VS2019_BIN_ARM)\link.exe
*_VS2019_ARM_APP_PATH             = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_PP_PATH              = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_ASM_PATH             = DEF(VS2019_BIN_ARM)\armasm.exe
*_VS2019_ARM_ASLCC_PATH           = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_ASLPP_PATH           = DEF(VS2019_BIN_ARM)\cl.exe
*_VS2019_ARM_ASLDLINK_PATH        = DEF(VS2019_BIN_ARM)\link.exe

  DEBUG_VS2019_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2019_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2019_ARM_CC_FLAGS         = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2019_ARM_ASM_FLAGS      = /nologo /g
RELEASE_VS2019_ARM_ASM_FLAGS      = /nologo
NOOPT_VS2019_ARM_ASM_FLAGS        = /nologo

  DEBUG_VS2019_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG
RELEASE_VS2019_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data
NOOPT_VS2019_ARM_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG

#####################
# AARCH64 definitions
#####################
*_VS2019_AARCH64_CC_PATH           = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_VFRPP_PATH        = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_SLINK_PATH        = DEF(VS2019_BIN_AARCH64)\lib.exe
*_VS2019_AARCH64_DLINK_PATH        = DEF(VS2019_BIN_AARCH64)\link.exe
*_VS2019_AARCH64_APP_PATH          = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_PP_PATH           = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_ASM_PATH          = DEF(VS2019_BIN_AARCH64)\armasm64.exe
*_VS2019_AARCH64_ASLCC_PATH        = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_ASLPP_PATH        = DEF(VS2019_BIN_AARCH64)\cl.exe
*_VS2019_AARCH64_ASLDLINK_PATH     = DEF(VS2019_BIN_AARCH64)\link.exe

  DEBUG_VS2019_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2019_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2019_AARCH64_CC_FLAGS      = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2019_AARCH64_ASM_FLAGS   = /nologo /g
RELEASE_VS2019_AARCH64_ASM_FLAGS   = /nologo
NOOPT_VS2019_AARCH64_ASM_FLAGS     = /nologo

  DEBUG_VS2019_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG
RELEASE_VS2019_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /MERGE:.rdata=.data
NOOPT_VS2019_AARCH64_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG

####################################################################################
#   VS2022       - Microsoft Visual Studio 2022 with Intel ASL
#   ASL          - Intel ACPI Source Language Compiler (iasl.exe)
####################################################################################
#   VS2022           - Microsoft Visual Studio 2022 with Intel ASL
*_VS2022_*_*_FAMILY        = MSFT
*_VS2022_*_*_DLL           = DEF(VS2022_BIN_HOST)

*_VS2022_*_MAKE_PATH       = DEF(VS2022_BIN_HOST)\nmake.exe
*_VS2022_*_MAKE_FLAG       = /nologo
*_VS2022_*_RC_PATH         = DEF(RC_PATH)

*_VS2022_*_MAKE_FLAGS      = /nologo
*_VS2022_*_SLINK_FLAGS     = /NOLOGO /LTCG
*_VS2022_*_APP_FLAGS       = /nologo /E /TC
*_VS2022_*_PP_FLAGS        = /nologo /E /TC /FIAutoGen.h
*_VS2022_*_VFRPP_FLAGS     = /nologo /E /TC /DVFRCOMPILE /FI$(MODULE_NAME)StrDefs.h
*_VS2022_*_DLINK2_FLAGS    = /WHOLEARCHIVE
*_VS2022_*_ASM16_PATH      = DEF(VS2022_BIN_IA32)\ml.exe
*_VS2022_*_DEPS_FLAGS      = DEF(MSFT_DEPS_FLAGS)
##################
# ASL definitions
##################
*_VS2022_*_ASL_PATH        = DEF(WIN_IASL_BIN)
*_VS2022_*_ASL_FLAGS       = DEF(DEFAULT_WIN_ASL_FLAGS)
*_VS2022_*_ASL_OUTFLAGS    = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_VS2022_*_ASLCC_FLAGS     = DEF(MSFT_ASLCC_FLAGS)
*_VS2022_*_ASLPP_FLAGS     = DEF(MSFT_ASLPP_FLAGS)
*_VS2022_*_ASLDLINK_FLAGS  = DEF(MSFT_ASLDLINK_FLAGS)

##################
# IA32 definitions
##################
*_VS2022_IA32_CC_PATH      = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_VFRPP_PATH   = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_ASLCC_PATH   = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_ASLPP_PATH   = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_SLINK_PATH   = DEF(VS2022_BIN_IA32)\lib.exe
*_VS2022_IA32_DLINK_PATH   = DEF(VS2022_BIN_IA32)\link.exe
*_VS2022_IA32_ASLDLINK_PATH= DEF(VS2022_BIN_IA32)\link.exe
*_VS2022_IA32_APP_PATH     = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_PP_PATH      = DEF(VS2022_BIN_IA32)\cl.exe
*_VS2022_IA32_ASM_PATH     = DEF(VS2022_BIN_IA32)\ml.exe

      *_VS2022_IA32_MAKE_FLAGS  = /nologo
  DEBUG_VS2022_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Gw
RELEASE_VS2022_IA32_CC_FLAGS    = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw
NOOPT_VS2022_IA32_CC_FLAGS      = /nologo /arch:IA32 /c /WX /GS /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Z7 /Od

  DEBUG_VS2022_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd /Zi
RELEASE_VS2022_IA32_ASM_FLAGS   = /nologo /c /WX /W3 /Cx /coff /Zd
NOOPT_VS2022_IA32_ASM_FLAGS     = /nologo /c /WX /W3 /Cx /coff /Zd /Zi

  DEBUG_VS2022_IA32_NASM_FLAGS  = -Ox -f win32 -g
RELEASE_VS2022_IA32_NASM_FLAGS  = -Ox -f win32
NOOPT_VS2022_IA32_NASM_FLAGS    = -O0 -f win32 -g

# Linker warning 4210 is disabled globally, because it checks if static initializers are present and the VCRuntime is
# linked. We do not link the VCRuntime (except for HOST_APPLICATIONs) so this warning can be generated erroneously
# whenever there are static initializers, because we will always fail the VCRuntime linking check
  DEBUG_VS2022_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /WX /IGNORE:4210
RELEASE_VS2022_IA32_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data /WX /IGNORE:4210
NOOPT_VS2022_IA32_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /WX /IGNORE:4210

# BaseTools cannot handle XIP modules with different section and file alignment
*_VS2022_IA32_DLINK_XIPFLAGS    = /ALIGN:32

##################
# X64 definitions
##################
*_VS2022_X64_CC_PATH       = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_PP_PATH       = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_APP_PATH      = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_VFRPP_PATH    = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_ASLCC_PATH    = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_ASLPP_PATH    = DEF(VS2022_BIN_X64)\cl.exe
*_VS2022_X64_ASM_PATH      = DEF(VS2022_BIN_X64)\ml64.exe
*_VS2022_X64_SLINK_PATH    = DEF(VS2022_BIN_X64)\lib.exe
*_VS2022_X64_DLINK_PATH    = DEF(VS2022_BIN_X64)\link.exe
*_VS2022_X64_ASLDLINK_PATH = DEF(VS2022_BIN_X64)\link.exe

  DEBUG_VS2022_X64_CC_FLAGS     = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Gw /volatileMetadata-
RELEASE_VS2022_X64_CC_FLAGS     = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /O1b2s /GL /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Gw /volatileMetadata-
NOOPT_VS2022_X64_CC_FLAGS       = /nologo /c /WX /GS /W4 /Gs32768 /D UNICODE /Gy /FIAutoGen.h /EHs-c- /GR- /GF /Z7 /Od /volatileMetadata-

  DEBUG_VS2022_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd /Zi
RELEASE_VS2022_X64_ASM_FLAGS    = /nologo /c /WX /W3 /Cx /Zd
NOOPT_VS2022_X64_ASM_FLAGS      = /nologo /c /WX /W3 /Cx /Zd /Zi

  DEBUG_VS2022_X64_NASM_FLAGS   = -Ox -f win64 -g
RELEASE_VS2022_X64_NASM_FLAGS   = -Ox -f win64
NOOPT_VS2022_X64_NASM_FLAGS     = -O0 -f win64 -g

# Linker warning 4210 is disabled globally, because it checks if static initializers are present and the VCRuntime is
# linked. We do not link the VCRuntime (except for HOST_APPLICATIONs) so this warning can be generated erroneously
# whenever there are static initializers, because we will always fail the VCRuntime linking check
  DEBUG_VS2022_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /ALIGN:4096 /DLL /WX /IGNORE:4210
RELEASE_VS2022_X64_DLINK_FLAGS  = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data /ALIGN:4096 /DLL /WX /IGNORE:4210
NOOPT_VS2022_X64_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4281 /OPT:REF /OPT:ICF=10 /MAP /ALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /ALIGN:4096 /DLL /WX /IGNORE:4210

# BaseTools cannot handle XIP modules with different section and file alignment
*_VS2022_X64_DLINK_XIPFLAGS     = /ALIGN:32

#################
# ARM definitions
#################
*_VS2022_ARM_CC_PATH              = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_VFRPP_PATH           = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_SLINK_PATH           = DEF(VS2022_BIN_ARM)\lib.exe
*_VS2022_ARM_DLINK_PATH           = DEF(VS2022_BIN_ARM)\link.exe
*_VS2022_ARM_APP_PATH             = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_PP_PATH              = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_ASM_PATH             = DEF(VS2022_BIN_ARM)\armasm.exe
*_VS2022_ARM_ASLCC_PATH           = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_ASLPP_PATH           = DEF(VS2022_BIN_ARM)\cl.exe
*_VS2022_ARM_ASLDLINK_PATH        = DEF(VS2022_BIN_ARM)\link.exe

      *_VS2022_ARM_MAKE_FLAGS     = /nologo
  DEBUG_VS2022_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2022_ARM_CC_FLAGS       = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2022_ARM_CC_FLAGS         = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2022_ARM_ASM_FLAGS      = /nologo /g
RELEASE_VS2022_ARM_ASM_FLAGS      = /nologo
NOOPT_VS2022_ARM_ASM_FLAGS        = /nologo

# Linker warning 4210 is disabled globally, because it checks if static initializers are present and the VCRuntime is
# linked. We do not link the VCRuntime (except for HOST_APPLICATIONs) so this warning can be generated erroneously
# whenever there are static initializers, because we will always fail the VCRuntime linking check
  DEBUG_VS2022_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /WX /IGNORE:4210
RELEASE_VS2022_ARM_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /MERGE:.rdata=.data /WX /IGNORE:4210
NOOPT_VS2022_ARM_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DRIVER /DEBUG /WX /IGNORE:4210

# BaseTools cannot handle XIP modules with different section and file alignment
*_VS2022_ARM_DLINK_XIPFLAGS       = /ALIGN:64

#####################
# AARCH64 definitions
#####################
*_VS2022_AARCH64_CC_PATH           = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_VFRPP_PATH        = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_SLINK_PATH        = DEF(VS2022_BIN_AARCH64)\lib.exe
*_VS2022_AARCH64_DLINK_PATH        = DEF(VS2022_BIN_AARCH64)\link.exe
*_VS2022_AARCH64_APP_PATH          = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_PP_PATH           = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_ASM_PATH          = DEF(VS2022_BIN_AARCH64)\armasm64.exe
*_VS2022_AARCH64_ASLCC_PATH        = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_ASLPP_PATH        = DEF(VS2022_BIN_AARCH64)\cl.exe
*_VS2022_AARCH64_ASLDLINK_PATH     = DEF(VS2022_BIN_AARCH64)\link.exe

      *_VS2022_AARCH64_MAKE_FLAGS  = /nologo
  DEBUG_VS2022_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Gw /Oi-
RELEASE_VS2022_AARCH64_CC_FLAGS    = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /O1b2 /GL /FIAutoGen.h /EHs-c- /GR- /GF /Gw /Oi-
NOOPT_VS2022_AARCH64_CC_FLAGS      = /nologo /c /WX /GS- /W4 /Gs32768 /D UNICODE /FIAutoGen.h /EHs-c- /GR- /GF /Gy /Zi /Od /Oi-

  DEBUG_VS2022_AARCH64_ASM_FLAGS   = /nologo /g
RELEASE_VS2022_AARCH64_ASM_FLAGS   = /nologo
NOOPT_VS2022_AARCH64_ASM_FLAGS     = /nologo

# Linker warning 4210 is disabled globally, because it checks if static initializers are present and the VCRuntime is
# linked. We do not link the VCRuntime (except for HOST_APPLICATIONs) so this warning can be generated erroneously
# whenever there are static initializers, because we will always fail the VCRuntime linking check
  DEBUG_VS2022_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4226 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG /WX /IGNORE:4210
RELEASE_VS2022_AARCH64_DLINK_FLAGS = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4226 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /MERGE:.rdata=.data /WX /IGNORE:4210
NOOPT_VS2022_AARCH64_DLINK_FLAGS   = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4226 /OPT:REF /OPT:ICF=10 /MAP /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:ARM64 /LTCG /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /DRIVER /DEBUG /WX /IGNORE:4210

# BaseTools cannot handle XIP modules with different section and file alignment
*_VS2022_AARCH64_DLINK_XIPFLAGS    = /ALIGN:64

####################################################################################
# GCC Common
####################################################################################

*_*_*_OBJCOPY_PATH              = echo
*_*_*_OBJCOPY_FLAGS             = objcopy not needed for
*_*_*_SYMRENAME_PATH            = echo
*_*_*_SYMRENAME_FLAGS           = Symbol renaming not needed for
DEBUG_*_*_OBJCOPY_ADDDEBUGFLAG     = --add-gnu-debuglink="$(DEBUG_DIR)/$(MODULE_NAME).debug"
RELEASE_*_*_OBJCOPY_ADDDEBUGFLAG   =
NOOPT_*_*_OBJCOPY_ADDDEBUGFLAG     = --add-gnu-debuglink="$(DEBUG_DIR)/$(MODULE_NAME).debug"
*_*_*_OBJCOPY_STRIPFLAG            = --strip-unneeded -R .eh_frame
*_*_*_DTC_FLAGS                    = -H epapr
*_*_*_DTCPP_PATH                   = DEF(DTCPP_BIN)
*_*_*_DTC_PATH                     = DEF(DTC_BIN)

# All supported GCC archs except LOONGARCH64 support -mstack-protector-guard=global, so set that on everything except LOONGARCH64
DEFINE GCC_ALL_CC_FLAGS            = -g -Os -fshort-wchar -fno-builtin -fno-strict-aliasing -Wall -Werror -Wno-array-bounds -include AutoGen.h -fno-common -fstack-protector
DEFINE GCC_IA32_X64_CC_FLAGS       = -mstack-protector-guard=global
DEFINE GCC_ARM_CC_FLAGS            = DEF(GCC_ALL_CC_FLAGS) -mlittle-endian -mabi=aapcs -fno-short-enums -funsigned-char -ffunction-sections -fdata-sections -fomit-frame-pointer -Wno-address -mthumb -fno-pic -fno-pie -mstack-protector-guard=global
DEFINE GCC_LOONGARCH64_CC_FLAGS    = DEF(GCC_ALL_CC_FLAGS) -mabi=lp64d -fno-asynchronous-unwind-tables -Wno-address -fno-short-enums -fsigned-char -ffunction-sections -fdata-sections
DEFINE GCC_ARM_CC_XIPFLAGS         = -mno-unaligned-access
DEFINE GCC_AARCH64_CC_FLAGS        = DEF(GCC_ALL_CC_FLAGS) -mlittle-endian -fno-short-enums -fverbose-asm -funsigned-char  -ffunction-sections -fdata-sections -Wno-address -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-pic -fno-pie -ffixed-x18 -mstack-protector-guard=global
DEFINE GCC_AARCH64_CC_XIPFLAGS     = -mstrict-align -mgeneral-regs-only
DEFINE GCC_RISCV64_CC_XIPFLAGS     = -mstrict-align -mgeneral-regs-only
DEFINE GCC_DLINK2_FLAGS_COMMON     = -Wl,--script=$(EDK_TOOLS_PATH)/Scripts/GccBase.lds
DEFINE GCC_IA32_X64_DLINK_COMMON   = -nostdlib --pie --fatal-warnings -z,noexecstack --gc-sections
DEFINE GCC_ARM_AARCH64_DLINK_COMMON= -Wl,--emit-relocs -nostdlib -Wl,--gc-sections -u $(IMAGE_ENTRY_POINT) -Wl,-e,$(IMAGE_ENTRY_POINT),-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map -Wl,--fatal-warnings -Wl,-z,noexecstack
DEFINE GCC_LOONGARCH64_DLINK_COMMON= -Wl,--emit-relocs -nostdlib -Wl,--gc-sections -u $(IMAGE_ENTRY_POINT) -Wl,-e,$(IMAGE_ENTRY_POINT),-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map
DEFINE GCC_ARM_DLINK_FLAGS         = DEF(GCC_ARM_AARCH64_DLINK_COMMON) -z common-page-size=0x20 -Wl,--pic-veneer
DEFINE GCC_AARCH64_DLINK_FLAGS     = DEF(GCC_ARM_AARCH64_DLINK_COMMON) -z common-page-size=0x20
DEFINE GCC_LOONGARCH64_DLINK_FLAGS = DEF(GCC_LOONGARCH64_DLINK_COMMON) -z common-page-size=0x20
DEFINE GCC_ARM_AARCH64_ASLDLINK_FLAGS = -Wl,--defsym=PECOFF_HEADER_SIZE=0 DEF(GCC_DLINK2_FLAGS_COMMON) -z common-page-size=0x20
DEFINE GCC_IA32_X64_ASLDLINK_FLAGS = DEF(GCC_IA32_X64_DLINK_COMMON) --entry _ReferenceAcpiTable -u $(IMAGE_ENTRY_POINT)
DEFINE GCC_ARM_ASLDLINK_FLAGS      = DEF(GCC_ARM_DLINK_FLAGS) -Wl,--entry,ReferenceAcpiTable -u $(IMAGE_ENTRY_POINT) DEF(GCC_ARM_AARCH64_ASLDLINK_FLAGS)
DEFINE GCC_AARCH64_ASLDLINK_FLAGS  = DEF(GCC_AARCH64_DLINK_FLAGS) -Wl,--entry,ReferenceAcpiTable -u $(IMAGE_ENTRY_POINT) DEF(GCC_ARM_AARCH64_ASLDLINK_FLAGS)
DEFINE GCC_LOONGARCH64_ASLDLINK_FLAGS = DEF(GCC_LOONGARCH64_DLINK_FLAGS) -Wl,--entry,ReferenceAcpiTable -u $(IMAGE_ENTRY_POINT)
DEFINE GCC_IA32_X64_DLINK_FLAGS    = DEF(GCC_IA32_X64_DLINK_COMMON) --entry _$(IMAGE_ENTRY_POINT) --file-alignment 0x20 --section-alignment 0x20 -Map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
DEFINE GCC_ASM_FLAGS               = -c -x assembler -imacros AutoGen.h
DEFINE GCC_PP_FLAGS                = -E -x assembler-with-cpp -include AutoGen.h
DEFINE GCC_VFRPP_FLAGS             = -x c -E -DVFRCOMPILE --include $(MODULE_NAME)StrDefs.h
DEFINE GCC_ASLPP_FLAGS             = -x c -E -include AutoGen.h
DEFINE GCC_ASLCC_FLAGS             = -x c
DEFINE GCC_WINDRES_FLAGS           = -J rc -O coff
DEFINE GCC_DTCPP_FLAGS             = -E -x assembler-with-cpp -imacros AutoGen.h -nostdinc -undef
DEFINE GCC_IA32_RC_FLAGS           = -I binary -O elf32-i386          -B i386        --rename-section .data=.hii
DEFINE GCC_X64_RC_FLAGS            = -I binary -O elf64-x86-64        -B i386        --rename-section .data=.hii
DEFINE GCC_ARM_RC_FLAGS            = -I binary -O elf32-littlearm     -B arm         --rename-section .data=.hii
DEFINE GCC_AARCH64_RC_FLAGS        = -I binary -O elf64-littleaarch64 -B aarch64     --rename-section .data=.hii
DEFINE GCC_AARCH64_RC_BTI_FLAGS    = --add-section .note.gnu.property=$(WORKSPACE)/BaseTools/Bin/GnuNoteBti.bin --set-section-flags .note.gnu.property=alloc,readonly
DEFINE GCC_RISCV64_RC_FLAGS        = -I binary -O elf64-littleriscv   -B riscv       --rename-section .data=.hii
DEFINE GCC_LOONGARCH64_RC_FLAGS    = -I binary -O elf64-loongarch     -B loongarch64 --rename-section .data=.hii

# GCC Build Flag for included header file list generation
DEFINE GCC_DEPS_FLAGS              = -MMD -MF $@.deps

DEFINE GCC48_ALL_CC_FLAGS            = DEF(GCC_ALL_CC_FLAGS) -ffunction-sections -fdata-sections -DSTRING_ARRAY_NAME=$(BASE_NAME)Strings
DEFINE GCC48_IA32_X64_DLINK_COMMON   = -nostdlib -Wl,-n,-q,--gc-sections -z common-page-size=0x20
DEFINE GCC48_IA32_CC_FLAGS           = DEF(GCC48_ALL_CC_FLAGS) DEF(GCC_IA32_X64_CC_FLAGS) -m32 -march=i586 -malign-double -D EFI32 -fno-asynchronous-unwind-tables -Wno-address -fno-omit-frame-pointer
DEFINE GCC48_X64_CC_FLAGS            = DEF(GCC48_ALL_CC_FLAGS) DEF(GCC_IA32_X64_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -maccumulate-outgoing-args -mno-red-zone -Wno-address -mcmodel=small -fpie -fno-asynchronous-unwind-tables -Wno-address  -fno-omit-frame-pointer
DEFINE GCC48_IA32_X64_ASLDLINK_FLAGS = DEF(GCC48_IA32_X64_DLINK_COMMON) -Wl,--entry,ReferenceAcpiTable -u ReferenceAcpiTable
DEFINE GCC48_IA32_X64_DLINK_FLAGS    = DEF(GCC48_IA32_X64_DLINK_COMMON) -Wl,--entry,$(IMAGE_ENTRY_POINT) -u $(IMAGE_ENTRY_POINT) -Wl,-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map,--whole-archive
DEFINE GCC48_IA32_DLINK2_FLAGS       = -Wl,--defsym=PECOFF_HEADER_SIZE=0x220 DEF(GCC_DLINK2_FLAGS_COMMON)
DEFINE GCC48_X64_DLINK_FLAGS         = DEF(GCC48_IA32_X64_DLINK_FLAGS) -Wl,-melf_x86_64,--oformat=elf64-x86-64,-pie
DEFINE GCC48_X64_DLINK2_FLAGS        = -Wl,--defsym=PECOFF_HEADER_SIZE=0x228 DEF(GCC_DLINK2_FLAGS_COMMON)
DEFINE GCC48_ASM_FLAGS               = DEF(GCC_ASM_FLAGS)
DEFINE GCC48_ARM_ASM_FLAGS           = $(PLATFORM_FLAGS) DEF(GCC_ASM_FLAGS) -mlittle-endian
DEFINE GCC48_AARCH64_ASM_FLAGS       = $(PLATFORM_FLAGS) DEF(GCC_ASM_FLAGS) -mlittle-endian
DEFINE GCC48_ARM_CC_FLAGS            = $(PLATFORM_FLAGS) DEF(GCC_ARM_CC_FLAGS) -mword-relocations
DEFINE GCC48_ARM_CC_XIPFLAGS         = DEF(GCC_ARM_CC_XIPFLAGS)
DEFINE GCC48_AARCH64_CC_FLAGS        = $(PLATFORM_FLAGS) -mcmodel=large DEF(GCC_AARCH64_CC_FLAGS)
DEFINE GCC48_AARCH64_CC_XIPFLAGS     = DEF(GCC_AARCH64_CC_XIPFLAGS)
DEFINE GCC48_ARM_DLINK_FLAGS         = DEF(GCC_ARM_DLINK_FLAGS) -Wl,--oformat=elf32-littlearm
DEFINE GCC48_ARM_DLINK2_FLAGS        = DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x220
DEFINE GCC48_AARCH64_DLINK_FLAGS     = DEF(GCC_AARCH64_DLINK_FLAGS)
DEFINE GCC48_AARCH64_DLINK2_FLAGS    = DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x228
DEFINE GCC48_ARM_ASLDLINK_FLAGS      = DEF(GCC_ARM_ASLDLINK_FLAGS) -Wl,--oformat=elf32-littlearm
DEFINE GCC48_AARCH64_ASLDLINK_FLAGS  = DEF(GCC_AARCH64_ASLDLINK_FLAGS)
DEFINE GCC48_ASLCC_FLAGS             = DEF(GCC_ASLCC_FLAGS)

DEFINE GCC49_IA32_CC_FLAGS           = DEF(GCC48_IA32_CC_FLAGS) -fno-pic -fno-pie
DEFINE GCC49_X64_CC_FLAGS            = DEF(GCC48_X64_CC_FLAGS)
DEFINE GCC49_IA32_X64_DLINK_COMMON   = -nostdlib -Wl,-n,-q,--gc-sections -z common-page-size=0x40
DEFINE GCC49_IA32_X64_ASLDLINK_FLAGS = DEF(GCC49_IA32_X64_DLINK_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0 DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--entry,ReferenceAcpiTable -u ReferenceAcpiTable
DEFINE GCC49_IA32_X64_DLINK_FLAGS    = DEF(GCC49_IA32_X64_DLINK_COMMON) -Wl,--entry,$(IMAGE_ENTRY_POINT) -u $(IMAGE_ENTRY_POINT) -Wl,-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map,--whole-archive
DEFINE GCC49_IA32_DLINK2_FLAGS       = DEF(GCC48_IA32_DLINK2_FLAGS)
DEFINE GCC49_X64_DLINK_FLAGS         = DEF(GCC49_IA32_X64_DLINK_FLAGS) -Wl,-melf_x86_64,--oformat=elf64-x86-64,-pie
DEFINE GCC49_X64_DLINK2_FLAGS        = DEF(GCC48_X64_DLINK2_FLAGS)
DEFINE GCC49_ASM_FLAGS               = DEF(GCC48_ASM_FLAGS)
DEFINE GCC49_ARM_ASM_FLAGS           = DEF(GCC48_ARM_ASM_FLAGS)
DEFINE GCC49_AARCH64_ASM_FLAGS       = DEF(GCC48_AARCH64_ASM_FLAGS)
DEFINE GCC49_ARM_CC_FLAGS            = DEF(GCC48_ARM_CC_FLAGS)
DEFINE GCC49_ARM_CC_XIPFLAGS         = DEF(GCC48_ARM_CC_XIPFLAGS)
DEFINE GCC49_AARCH64_CC_FLAGS        = $(PLATFORM_FLAGS) DEF(GCC48_ALL_CC_FLAGS) DEF(GCC_AARCH64_CC_FLAGS) -mcmodel=small
DEFINE GCC49_AARCH64_CC_XIPFLAGS     = DEF(GCC48_AARCH64_CC_XIPFLAGS)
DEFINE GCC49_ARM_DLINK_FLAGS         = DEF(GCC48_ARM_DLINK_FLAGS)
DEFINE GCC49_ARM_DLINK2_FLAGS        = DEF(GCC48_ARM_DLINK2_FLAGS)
DEFINE GCC49_AARCH64_DLINK_FLAGS     = DEF(GCC48_AARCH64_DLINK_FLAGS) -z common-page-size=0x1000
DEFINE GCC49_AARCH64_DLINK2_FLAGS    = DEF(GCC48_AARCH64_DLINK2_FLAGS)
DEFINE GCC49_ARM_ASLDLINK_FLAGS      = DEF(GCC48_ARM_ASLDLINK_FLAGS)
DEFINE GCC49_AARCH64_ASLDLINK_FLAGS  = DEF(GCC48_AARCH64_ASLDLINK_FLAGS)
DEFINE GCC49_ASLCC_FLAGS             = DEF(GCC48_ASLCC_FLAGS)

DEFINE GCC5_IA32_CC_FLAGS            = DEF(GCC49_IA32_CC_FLAGS)
DEFINE GCC5_X64_CC_FLAGS             = DEF(GCC49_X64_CC_FLAGS)
DEFINE GCC5_IA32_X64_DLINK_COMMON    = DEF(GCC49_IA32_X64_DLINK_COMMON)
DEFINE GCC5_IA32_X64_ASLDLINK_FLAGS  = DEF(GCC49_IA32_X64_ASLDLINK_FLAGS)
DEFINE GCC5_IA32_X64_DLINK_FLAGS     = DEF(GCC49_IA32_X64_DLINK_FLAGS)
DEFINE GCC5_IA32_DLINK2_FLAGS        = DEF(GCC49_IA32_DLINK2_FLAGS) -Wno-error
DEFINE GCC5_X64_DLINK_FLAGS          = DEF(GCC49_X64_DLINK_FLAGS)
DEFINE GCC5_X64_DLINK2_FLAGS         = DEF(GCC49_X64_DLINK2_FLAGS) -Wno-error
DEFINE GCC5_ASM_FLAGS                = DEF(GCC49_ASM_FLAGS)
DEFINE GCC5_ARM_ASM_FLAGS            = DEF(GCC49_ARM_ASM_FLAGS)
DEFINE GCC5_AARCH64_ASM_FLAGS        = DEF(GCC49_AARCH64_ASM_FLAGS)
DEFINE GCC5_ARM_CC_FLAGS             = DEF(GCC49_ARM_CC_FLAGS)
DEFINE GCC5_ARM_CC_XIPFLAGS          = DEF(GCC49_ARM_CC_XIPFLAGS)
DEFINE GCC5_AARCH64_CC_FLAGS         = DEF(GCC49_AARCH64_CC_FLAGS)
DEFINE GCC5_AARCH64_CC_XIPFLAGS      = DEF(GCC49_AARCH64_CC_XIPFLAGS)
DEFINE GCC5_ARM_DLINK_FLAGS          = DEF(GCC49_ARM_DLINK_FLAGS)
DEFINE GCC5_ARM_DLINK2_FLAGS         = DEF(GCC49_ARM_DLINK2_FLAGS) -Wno-error
DEFINE GCC5_AARCH64_DLINK_FLAGS      = DEF(GCC49_AARCH64_DLINK_FLAGS)
DEFINE GCC5_AARCH64_DLINK2_FLAGS     = DEF(GCC49_AARCH64_DLINK2_FLAGS) -Wno-error
DEFINE GCC5_ARM_ASLDLINK_FLAGS       = DEF(GCC49_ARM_ASLDLINK_FLAGS)
DEFINE GCC5_AARCH64_ASLDLINK_FLAGS   = DEF(GCC49_AARCH64_ASLDLINK_FLAGS)
DEFINE GCC5_ASLCC_FLAGS              = DEF(GCC49_ASLCC_FLAGS) -fno-lto

DEFINE GCC5_RISCV_ALL_CC_FLAGS                    = -g -fshort-wchar -fno-strict-aliasing -Wall -Werror -Wno-array-bounds -ffunction-sections -fdata-sections -include AutoGen.h -fno-common -DSTRING_ARRAY_NAME=$(BASE_NAME)Strings -msmall-data-limit=0
DEFINE GCC5_RISCV_ALL_DLINK_COMMON                = -nostdlib -Wl,-n,-q,--gc-sections -z common-page-size=0x40
DEFINE GCC5_RISCV_ALL_DLINK_FLAGS                 = DEF(GCC5_RISCV_ALL_DLINK_COMMON) -Wl,--entry,$(IMAGE_ENTRY_POINT) -u $(IMAGE_ENTRY_POINT) -Wl,-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map
DEFINE GCC5_RISCV_ALL_DLINK2_FLAGS                = -Wl,--defsym=PECOFF_HEADER_SIZE=0x220,--script=$(EDK_TOOLS_PATH)/Scripts/GccBase.lds
DEFINE GCC5_RISCV_ALL_ASM_FLAGS                   = -c -x assembler -imacros $(DEST_DIR_DEBUG)/AutoGen.h
DEFINE GCC5_RISCV_ALL_CC_FLAGS_WARNING_DISABLE    = -Wno-tautological-compare -Wno-pointer-compare

DEFINE GCC5_RISCV_OPENSBI_TYPES                   = -DOPENSBI_EXTERNAL_SBI_TYPES=OpensbiTypes.h

DEFINE GCC5_RISCV64_ARCH                   = rv64gc
DEFINE GCC5_RISCV32_RISCV64_ASLDLINK_FLAGS = DEF(GCC5_RISCV_ALL_DLINK_COMMON) -Wl,--entry,ReferenceAcpiTable -u ReferenceAcpiTable
DEFINE GCC5_RISCV64_CC_FLAGS               = DEF(GCC5_RISCV_ALL_CC_FLAGS) DEF(GCC5_RISCV_ALL_CC_FLAGS_WARNING_DISABLE) DEF(GCC5_RISCV_OPENSBI_TYPES) -march=DEF(GCC5_RISCV64_ARCH) -fno-builtin -fno-builtin-memcpy -fno-stack-protector -Wno-address -fno-asynchronous-unwind-tables -fno-unwind-tables -Wno-unused-but-set-variable -fpack-struct=8 -mcmodel=medany -mabi=lp64 -mno-relax
DEFINE GCC5_RISCV64_DLINK_FLAGS            = DEF(GCC5_RISCV_ALL_DLINK_FLAGS) -Wl,-melf64lriscv,--oformat=elf64-littleriscv,--no-relax
DEFINE GCC5_RISCV64_DLINK2_FLAGS           = DEF(GCC5_RISCV_ALL_DLINK2_FLAGS)
DEFINE GCC5_RISCV64_ASM_FLAGS              = DEF(GCC5_RISCV_ALL_ASM_FLAGS) -march=DEF(GCC5_RISCV64_ARCH) -mcmodel=medany -mabi=lp64

DEFINE GCC5_LOONGARCH64_CC_FLAGS           = DEF(GCC_LOONGARCH64_CC_FLAGS) -march=loongarch64 -mno-memcpy -Werror -Wno-maybe-uninitialized -Wno-stringop-overflow -Wno-pointer-to-int-cast -no-pie -fno-stack-protector -mno-explicit-relocs
DEFINE GCC5_LOONGARCH64_DLINK_FLAGS        = DEF(GCC_LOONGARCH64_DLINK_FLAGS)
DEFINE GCC5_LOONGARCH64_DLINK2_FLAGS       = DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x228
DEFINE GCC5_LOONGARCH64_ASLDLINK_FLAGS     = DEF(GCC_LOONGARCH64_ASLDLINK_FLAGS) DEF(GCC5_LOONGARCH64_DLINK2_FLAGS)
DEFINE GCC5_LOONGARCH64_ASM_FLAGS          = -x assembler-with-cpp -mabi=lp64d -march=loongarch64 -fno-builtin -c -Wall -mno-explicit-relocs
DEFINE GCC5_LOONGARCH64_PP_FLAGS           = -mabi=lp64d -march=loongarch64 DEF(GCC_PP_FLAGS)

####################################################################################
#
# GCC 4.8 - This configuration is used to compile under Linux to produce
#           PE/COFF binaries using GCC 4.8.
#
####################################################################################
*_GCC48_*_*_FAMILY               = GCC

*_GCC48_*_MAKE_PATH                    = DEF(GCC_HOST_PREFIX)make
*_GCC48_*_*_DLL                        = ENV(GCC48_DLL)
*_GCC48_*_ASL_PATH                     = DEF(UNIX_IASL_BIN)

*_GCC48_*_PP_FLAGS                     = DEF(GCC_PP_FLAGS)
*_GCC48_*_ASLPP_FLAGS                  = DEF(GCC_ASLPP_FLAGS)
*_GCC48_*_ASLCC_FLAGS                  = DEF(GCC_ASLCC_FLAGS)
*_GCC48_*_VFRPP_FLAGS                  = DEF(GCC_VFRPP_FLAGS)
*_GCC48_*_APP_FLAGS                    =
*_GCC48_*_ASL_FLAGS                    = DEF(IASL_FLAGS)
*_GCC48_*_ASL_OUTFLAGS                 = DEF(IASL_OUTFLAGS)
*_GCC48_*_DEPS_FLAGS                   = DEF(GCC_DEPS_FLAGS)

##################
# GCC48 IA32 definitions
##################
*_GCC48_IA32_OBJCOPY_PATH         = DEF(GCC48_IA32_PREFIX)objcopy
*_GCC48_IA32_CC_PATH              = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_SLINK_PATH           = DEF(GCC48_IA32_PREFIX)ar
*_GCC48_IA32_DLINK_PATH           = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_ASLDLINK_PATH        = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_ASM_PATH             = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_PP_PATH              = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_VFRPP_PATH           = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_ASLCC_PATH           = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_ASLPP_PATH           = DEF(GCC48_IA32_PREFIX)gcc
*_GCC48_IA32_RC_PATH              = DEF(GCC48_IA32_PREFIX)objcopy

*_GCC48_IA32_ASLCC_FLAGS          = DEF(GCC48_ASLCC_FLAGS) -m32
*_GCC48_IA32_ASLDLINK_FLAGS       = DEF(GCC48_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386
*_GCC48_IA32_ASM_FLAGS            = DEF(GCC48_ASM_FLAGS) -m32 -march=i386
*_GCC48_IA32_DLINK_FLAGS          = DEF(GCC48_IA32_X64_DLINK_FLAGS) -Wl,-m,elf_i386,--oformat=elf32-i386
*_GCC48_IA32_DLINK2_FLAGS         = DEF(GCC48_IA32_DLINK2_FLAGS)
*_GCC48_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_GCC48_IA32_OBJCOPY_FLAGS        =
*_GCC48_IA32_NASM_FLAGS           = -f elf32

  DEBUG_GCC48_IA32_CC_FLAGS       = DEF(GCC48_IA32_CC_FLAGS)
RELEASE_GCC48_IA32_CC_FLAGS       = DEF(GCC48_IA32_CC_FLAGS) -Wno-unused-but-set-variable
  NOOPT_GCC48_IA32_CC_FLAGS       = DEF(GCC48_IA32_CC_FLAGS) -O0

##################
# GCC48 X64 definitions
##################
*_GCC48_X64_OBJCOPY_PATH         = DEF(GCC48_X64_PREFIX)objcopy
*_GCC48_X64_CC_PATH              = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_SLINK_PATH           = DEF(GCC48_X64_PREFIX)ar
*_GCC48_X64_DLINK_PATH           = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_ASLDLINK_PATH        = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_ASM_PATH             = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_PP_PATH              = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_VFRPP_PATH           = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_ASLCC_PATH           = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_ASLPP_PATH           = DEF(GCC48_X64_PREFIX)gcc
*_GCC48_X64_RC_PATH              = DEF(GCC48_X64_PREFIX)objcopy

*_GCC48_X64_ASLCC_FLAGS          = DEF(GCC48_ASLCC_FLAGS) -m64
*_GCC48_X64_ASLDLINK_FLAGS       = DEF(GCC48_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64
*_GCC48_X64_ASM_FLAGS            = DEF(GCC48_ASM_FLAGS) -m64
*_GCC48_X64_DLINK_FLAGS          = DEF(GCC48_X64_DLINK_FLAGS)
*_GCC48_X64_DLINK2_FLAGS         = DEF(GCC48_X64_DLINK2_FLAGS)
*_GCC48_X64_RC_FLAGS             = DEF(GCC_X64_RC_FLAGS)
*_GCC48_X64_OBJCOPY_FLAGS        =
*_GCC48_X64_NASM_FLAGS           = -f elf64

  DEBUG_GCC48_X64_CC_FLAGS       = DEF(GCC48_X64_CC_FLAGS)
RELEASE_GCC48_X64_CC_FLAGS       = DEF(GCC48_X64_CC_FLAGS) -Wno-unused-but-set-variable
  NOOPT_GCC48_X64_CC_FLAGS       = DEF(GCC48_X64_CC_FLAGS) -O0

##################
# GCC48 ARM definitions
##################
*_GCC48_ARM_CC_PATH              = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_SLINK_PATH           = ENV(GCC48_ARM_PREFIX)ar
*_GCC48_ARM_DLINK_PATH           = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_ASLDLINK_PATH        = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_ASM_PATH             = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_PP_PATH              = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_VFRPP_PATH           = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_ASLCC_PATH           = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_ASLPP_PATH           = ENV(GCC48_ARM_PREFIX)gcc
*_GCC48_ARM_RC_PATH              = ENV(GCC48_ARM_PREFIX)objcopy

*_GCC48_ARM_ASLCC_FLAGS          = DEF(GCC48_ASLCC_FLAGS)
*_GCC48_ARM_ASLDLINK_FLAGS       = DEF(GCC48_ARM_ASLDLINK_FLAGS)
*_GCC48_ARM_ASM_FLAGS            = DEF(GCC48_ARM_ASM_FLAGS)
*_GCC48_ARM_DLINK_FLAGS          = DEF(GCC48_ARM_DLINK_FLAGS)
*_GCC48_ARM_DLINK2_FLAGS         = DEF(GCC48_ARM_DLINK2_FLAGS)
*_GCC48_ARM_DTCPP_FLAGS          = DEF(GCC_DTCPP_FLAGS)
*_GCC48_ARM_PLATFORM_FLAGS       = -march=armv7-a
*_GCC48_ARM_PP_FLAGS             = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC48_ARM_RC_FLAGS             = DEF(GCC_ARM_RC_FLAGS)
*_GCC48_ARM_VFRPP_FLAGS          = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC48_ARM_CC_XIPFLAGS          = DEF(GCC48_ARM_CC_XIPFLAGS)

  DEBUG_GCC48_ARM_CC_FLAGS       = DEF(GCC48_ARM_CC_FLAGS) -O0
RELEASE_GCC48_ARM_CC_FLAGS       = DEF(GCC48_ARM_CC_FLAGS) -Wno-unused-but-set-variable
  NOOPT_GCC48_ARM_CC_FLAGS       = DEF(GCC48_ARM_CC_FLAGS) -O0

##################
# GCC48 AARCH64 definitions
##################
*_GCC48_AARCH64_CC_PATH          = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_SLINK_PATH       = ENV(GCC48_AARCH64_PREFIX)ar
*_GCC48_AARCH64_DLINK_PATH       = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_ASLDLINK_PATH    = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_ASM_PATH         = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_PP_PATH          = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_VFRPP_PATH       = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_ASLCC_PATH       = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_ASLPP_PATH       = ENV(GCC48_AARCH64_PREFIX)gcc
*_GCC48_AARCH64_RC_PATH          = ENV(GCC48_AARCH64_PREFIX)objcopy

*_GCC48_AARCH64_ASLCC_FLAGS      = DEF(GCC48_ASLCC_FLAGS)
*_GCC48_AARCH64_ASLDLINK_FLAGS   = DEF(GCC48_AARCH64_ASLDLINK_FLAGS)
*_GCC48_AARCH64_ASM_FLAGS        = DEF(GCC48_AARCH64_ASM_FLAGS)
*_GCC48_AARCH64_DLINK_FLAGS      = DEF(GCC48_AARCH64_DLINK_FLAGS)
*_GCC48_AARCH64_DLINK2_FLAGS     = DEF(GCC48_AARCH64_DLINK2_FLAGS)
*_GCC48_AARCH64_DTCPP_FLAGS      = DEF(GCC_DTCPP_FLAGS)
*_GCC48_AARCH64_PLATFORM_FLAGS   =
*_GCC48_AARCH64_PP_FLAGS         = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC48_AARCH64_RC_FLAGS         = DEF(GCC_AARCH64_RC_FLAGS)
*_GCC48_AARCH64_VFRPP_FLAGS      = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC48_AARCH64_CC_XIPFLAGS      = DEF(GCC48_AARCH64_CC_XIPFLAGS)

  DEBUG_GCC48_AARCH64_CC_FLAGS   = DEF(GCC48_AARCH64_CC_FLAGS) -O0
RELEASE_GCC48_AARCH64_CC_FLAGS   = DEF(GCC48_AARCH64_CC_FLAGS) -Wno-unused-but-set-variable
  NOOPT_GCC48_AARCH64_CC_FLAGS   = DEF(GCC48_AARCH64_CC_FLAGS) -O0

####################################################################################
#
# GCC 4.9 - This configuration is used to compile under Linux to produce
#           PE/COFF binaries using GCC 4.9.
#
####################################################################################
*_GCC49_*_*_FAMILY               = GCC

*_GCC49_*_MAKE_PATH                    = DEF(GCC_HOST_PREFIX)make
*_GCC49_*_*_DLL                        = ENV(GCC49_DLL)
*_GCC49_*_ASL_PATH                     = DEF(UNIX_IASL_BIN)

*_GCC49_*_PP_FLAGS                     = DEF(GCC_PP_FLAGS)
*_GCC49_*_ASLPP_FLAGS                  = DEF(GCC_ASLPP_FLAGS)
*_GCC49_*_ASLCC_FLAGS                  = DEF(GCC_ASLCC_FLAGS)
*_GCC49_*_VFRPP_FLAGS                  = DEF(GCC_VFRPP_FLAGS)
*_GCC49_*_APP_FLAGS                    =
*_GCC49_*_ASL_FLAGS                    = DEF(IASL_FLAGS)
*_GCC49_*_ASL_OUTFLAGS                 = DEF(IASL_OUTFLAGS)
*_GCC49_*_DEPS_FLAGS                   = DEF(GCC_DEPS_FLAGS)

##################
# GCC49 IA32 definitions
##################
*_GCC49_IA32_OBJCOPY_PATH         = DEF(GCC49_IA32_PREFIX)objcopy
*_GCC49_IA32_CC_PATH              = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_SLINK_PATH           = DEF(GCC49_IA32_PREFIX)ar
*_GCC49_IA32_DLINK_PATH           = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_ASLDLINK_PATH        = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_ASM_PATH             = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_PP_PATH              = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_VFRPP_PATH           = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_ASLCC_PATH           = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_ASLPP_PATH           = DEF(GCC49_IA32_PREFIX)gcc
*_GCC49_IA32_RC_PATH              = DEF(GCC49_IA32_PREFIX)objcopy

*_GCC49_IA32_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS) -m32
*_GCC49_IA32_ASLDLINK_FLAGS       = DEF(GCC49_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386
*_GCC49_IA32_ASM_FLAGS            = DEF(GCC49_ASM_FLAGS) -m32 -march=i386
*_GCC49_IA32_DLINK_FLAGS          = DEF(GCC49_IA32_X64_DLINK_FLAGS) -Wl,-m,elf_i386,--oformat=elf32-i386
*_GCC49_IA32_DLINK2_FLAGS         = DEF(GCC49_IA32_DLINK2_FLAGS)
*_GCC49_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_GCC49_IA32_OBJCOPY_FLAGS        =
*_GCC49_IA32_NASM_FLAGS           = -f elf32

  DEBUG_GCC49_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS)
RELEASE_GCC49_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCC49_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS) -O0

##################
# GCC49 X64 definitions
##################
*_GCC49_X64_OBJCOPY_PATH         = DEF(GCC49_X64_PREFIX)objcopy
*_GCC49_X64_CC_PATH              = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_SLINK_PATH           = DEF(GCC49_X64_PREFIX)ar
*_GCC49_X64_DLINK_PATH           = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_ASLDLINK_PATH        = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_ASM_PATH             = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_PP_PATH              = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_VFRPP_PATH           = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_ASLCC_PATH           = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_ASLPP_PATH           = DEF(GCC49_X64_PREFIX)gcc
*_GCC49_X64_RC_PATH              = DEF(GCC49_X64_PREFIX)objcopy

*_GCC49_X64_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS) -m64
*_GCC49_X64_ASLDLINK_FLAGS       = DEF(GCC49_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64
*_GCC49_X64_ASM_FLAGS            = DEF(GCC49_ASM_FLAGS) -m64
*_GCC49_X64_DLINK_FLAGS          = DEF(GCC49_X64_DLINK_FLAGS)
*_GCC49_X64_DLINK2_FLAGS         = DEF(GCC49_X64_DLINK2_FLAGS)
*_GCC49_X64_RC_FLAGS             = DEF(GCC_X64_RC_FLAGS)
*_GCC49_X64_OBJCOPY_FLAGS        =
*_GCC49_X64_NASM_FLAGS           = -f elf64

  DEBUG_GCC49_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS)
RELEASE_GCC49_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCC49_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS) -O0

##################
# GCC49 ARM definitions
##################
*_GCC49_ARM_CC_PATH              = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_SLINK_PATH           = ENV(GCC49_ARM_PREFIX)ar
*_GCC49_ARM_DLINK_PATH           = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_ASLDLINK_PATH        = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_ASM_PATH             = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_PP_PATH              = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_VFRPP_PATH           = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_ASLCC_PATH           = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_ASLPP_PATH           = ENV(GCC49_ARM_PREFIX)gcc
*_GCC49_ARM_RC_PATH              = ENV(GCC49_ARM_PREFIX)objcopy

*_GCC49_ARM_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS)
*_GCC49_ARM_ASLDLINK_FLAGS       = DEF(GCC49_ARM_ASLDLINK_FLAGS)
*_GCC49_ARM_ASM_FLAGS            = DEF(GCC49_ARM_ASM_FLAGS)
*_GCC49_ARM_DLINK_FLAGS          = DEF(GCC49_ARM_DLINK_FLAGS)
*_GCC49_ARM_DLINK2_FLAGS         = DEF(GCC49_ARM_DLINK2_FLAGS)
*_GCC49_ARM_DTCPP_FLAGS          = DEF(GCC_DTCPP_FLAGS)
*_GCC49_ARM_PLATFORM_FLAGS       = -march=armv7-a
*_GCC49_ARM_PP_FLAGS             = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC49_ARM_RC_FLAGS             = DEF(GCC_ARM_RC_FLAGS)
*_GCC49_ARM_VFRPP_FLAGS          = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC49_ARM_CC_XIPFLAGS          = DEF(GCC49_ARM_CC_XIPFLAGS)

  DEBUG_GCC49_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -O0
RELEASE_GCC49_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCC49_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -O0

##################
# GCC49 AARCH64 definitions
##################
*_GCC49_AARCH64_CC_PATH          = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_SLINK_PATH       = ENV(GCC49_AARCH64_PREFIX)ar
*_GCC49_AARCH64_DLINK_PATH       = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_ASLDLINK_PATH    = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_ASM_PATH         = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_PP_PATH          = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_VFRPP_PATH       = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_ASLCC_PATH       = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_ASLPP_PATH       = ENV(GCC49_AARCH64_PREFIX)gcc
*_GCC49_AARCH64_RC_PATH          = ENV(GCC49_AARCH64_PREFIX)objcopy

*_GCC49_AARCH64_ASLCC_FLAGS      = DEF(GCC49_ASLCC_FLAGS)
*_GCC49_AARCH64_ASLDLINK_FLAGS   = DEF(GCC49_AARCH64_ASLDLINK_FLAGS)
*_GCC49_AARCH64_ASM_FLAGS        = DEF(GCC49_AARCH64_ASM_FLAGS)
*_GCC49_AARCH64_DLINK2_FLAGS     = DEF(GCC49_AARCH64_DLINK2_FLAGS)
*_GCC49_AARCH64_DTCPP_FLAGS      = DEF(GCC_DTCPP_FLAGS)
*_GCC49_AARCH64_PLATFORM_FLAGS   =
*_GCC49_AARCH64_PP_FLAGS         = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC49_AARCH64_RC_FLAGS         = DEF(GCC_AARCH64_RC_FLAGS)
*_GCC49_AARCH64_VFRPP_FLAGS      = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC49_AARCH64_CC_XIPFLAGS      = DEF(GCC49_AARCH64_CC_XIPFLAGS)

  DEBUG_GCC49_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -O0
  DEBUG_GCC49_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS)
  DEBUG_GCC49_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

RELEASE_GCC49_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC49_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS)
RELEASE_GCC49_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

  NOOPT_GCC49_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -O0
  NOOPT_GCC49_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS) -O0
  NOOPT_GCC49_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20 -O0

####################################################################################
#
# GCC NOLTO - This configuration is used to compile under Linux to produce
#             PE/COFF binaries using GCC without Link Time Optimization
#
####################################################################################
*_GCCNOLTO_*_*_FAMILY               = GCC

*_GCCNOLTO_*_MAKE_PATH                    = DEF(GCC_HOST_PREFIX)make
*_GCCNOLTO_*_*_DLL                        = ENV(GCCNOLTO_DLL)
*_GCCNOLTO_*_ASL_PATH                     = DEF(UNIX_IASL_BIN)

*_GCCNOLTO_*_PP_FLAGS                     = DEF(GCC_PP_FLAGS)
*_GCCNOLTO_*_ASLPP_FLAGS                  = DEF(GCC_ASLPP_FLAGS)
*_GCCNOLTO_*_ASLCC_FLAGS                  = DEF(GCC_ASLCC_FLAGS)
*_GCCNOLTO_*_VFRPP_FLAGS                  = DEF(GCC_VFRPP_FLAGS)
*_GCCNOLTO_*_APP_FLAGS                    =
*_GCCNOLTO_*_ASL_FLAGS                    = DEF(IASL_FLAGS)
*_GCCNOLTO_*_ASL_OUTFLAGS                 = DEF(IASL_OUTFLAGS)
*_GCCNOLTO_*_DEPS_FLAGS                   = DEF(GCC_DEPS_FLAGS)

##################
# GCCNOLTO IA32 definitions
##################
*_GCCNOLTO_IA32_OBJCOPY_PATH         = DEF(GCCNOLTO_IA32_PREFIX)objcopy
*_GCCNOLTO_IA32_CC_PATH              = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_SLINK_PATH           = DEF(GCCNOLTO_IA32_PREFIX)ar
*_GCCNOLTO_IA32_DLINK_PATH           = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_ASLDLINK_PATH        = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_ASM_PATH             = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_PP_PATH              = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_VFRPP_PATH           = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_ASLCC_PATH           = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_ASLPP_PATH           = DEF(GCCNOLTO_IA32_PREFIX)gcc
*_GCCNOLTO_IA32_RC_PATH              = DEF(GCCNOLTO_IA32_PREFIX)objcopy

*_GCCNOLTO_IA32_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS) -m32
*_GCCNOLTO_IA32_ASLDLINK_FLAGS       = DEF(GCC49_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386
*_GCCNOLTO_IA32_ASM_FLAGS            = DEF(GCC49_ASM_FLAGS) -m32 -march=i386
*_GCCNOLTO_IA32_DLINK_FLAGS          = DEF(GCC49_IA32_X64_DLINK_FLAGS) -Wl,-m,elf_i386,--oformat=elf32-i386
*_GCCNOLTO_IA32_DLINK2_FLAGS         = DEF(GCC49_IA32_DLINK2_FLAGS)
*_GCCNOLTO_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_GCCNOLTO_IA32_OBJCOPY_FLAGS        =
*_GCCNOLTO_IA32_NASM_FLAGS           = -f elf32

  DEBUG_GCCNOLTO_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS)
RELEASE_GCCNOLTO_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCCNOLTO_IA32_CC_FLAGS       = DEF(GCC49_IA32_CC_FLAGS) -O0

##################
# GCCNOLTO X64 definitions
##################
*_GCCNOLTO_X64_OBJCOPY_PATH         = DEF(GCCNOLTO_X64_PREFIX)objcopy
*_GCCNOLTO_X64_CC_PATH              = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_SLINK_PATH           = DEF(GCCNOLTO_X64_PREFIX)ar
*_GCCNOLTO_X64_DLINK_PATH           = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_ASLDLINK_PATH        = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_ASM_PATH             = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_PP_PATH              = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_VFRPP_PATH           = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_ASLCC_PATH           = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_ASLPP_PATH           = DEF(GCCNOLTO_X64_PREFIX)gcc
*_GCCNOLTO_X64_RC_PATH              = DEF(GCCNOLTO_X64_PREFIX)objcopy

*_GCCNOLTO_X64_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS) -m64
*_GCCNOLTO_X64_ASLDLINK_FLAGS       = DEF(GCC49_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64
*_GCCNOLTO_X64_ASM_FLAGS            = DEF(GCC49_ASM_FLAGS) -m64
*_GCCNOLTO_X64_DLINK_FLAGS          = DEF(GCC49_X64_DLINK_FLAGS)
*_GCCNOLTO_X64_DLINK2_FLAGS         = DEF(GCC49_X64_DLINK2_FLAGS)
*_GCCNOLTO_X64_RC_FLAGS             = DEF(GCC_X64_RC_FLAGS)
*_GCCNOLTO_X64_OBJCOPY_FLAGS        =
*_GCCNOLTO_X64_NASM_FLAGS           = -f elf64

  DEBUG_GCCNOLTO_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS)
RELEASE_GCCNOLTO_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCCNOLTO_X64_CC_FLAGS       = DEF(GCC49_X64_CC_FLAGS) -O0

##################
# GCCNOLTO ARM definitions
##################
*_GCCNOLTO_ARM_CC_PATH              = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_SLINK_PATH           = ENV(GCCNOLTO_ARM_PREFIX)ar
*_GCCNOLTO_ARM_DLINK_PATH           = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_ASLDLINK_PATH        = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_ASM_PATH             = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_PP_PATH              = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_VFRPP_PATH           = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_ASLCC_PATH           = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_ASLPP_PATH           = ENV(GCCNOLTO_ARM_PREFIX)gcc
*_GCCNOLTO_ARM_RC_PATH              = ENV(GCCNOLTO_ARM_PREFIX)objcopy

*_GCCNOLTO_ARM_ASLCC_FLAGS          = DEF(GCC49_ASLCC_FLAGS)
*_GCCNOLTO_ARM_ASLDLINK_FLAGS       = DEF(GCC49_ARM_ASLDLINK_FLAGS)
*_GCCNOLTO_ARM_ASM_FLAGS            = DEF(GCC49_ARM_ASM_FLAGS)
*_GCCNOLTO_ARM_DLINK_FLAGS          = DEF(GCC49_ARM_DLINK_FLAGS)
*_GCCNOLTO_ARM_DLINK2_FLAGS         = DEF(GCC49_ARM_DLINK2_FLAGS)
*_GCCNOLTO_ARM_DTCPP_FLAGS          = DEF(GCC_DTCPP_FLAGS)
*_GCCNOLTO_ARM_PLATFORM_FLAGS       = -march=armv7-a
*_GCCNOLTO_ARM_PP_FLAGS             = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCCNOLTO_ARM_RC_FLAGS             = DEF(GCC_ARM_RC_FLAGS)
*_GCCNOLTO_ARM_VFRPP_FLAGS          = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCCNOLTO_ARM_CC_XIPFLAGS          = DEF(GCC49_ARM_CC_XIPFLAGS)

  DEBUG_GCCNOLTO_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -O0
RELEASE_GCCNOLTO_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
  NOOPT_GCCNOLTO_ARM_CC_FLAGS       = DEF(GCC49_ARM_CC_FLAGS) -O0

##################
# GCCNOLTO AARCH64 definitions
##################
*_GCCNOLTO_AARCH64_CC_PATH          = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_SLINK_PATH       = ENV(GCCNOLTO_AARCH64_PREFIX)ar
*_GCCNOLTO_AARCH64_DLINK_PATH       = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_ASLDLINK_PATH    = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_ASM_PATH         = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_PP_PATH          = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_VFRPP_PATH       = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_ASLCC_PATH       = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_ASLPP_PATH       = ENV(GCCNOLTO_AARCH64_PREFIX)gcc
*_GCCNOLTO_AARCH64_RC_PATH          = ENV(GCCNOLTO_AARCH64_PREFIX)objcopy

*_GCCNOLTO_AARCH64_ASLCC_FLAGS      = DEF(GCC49_ASLCC_FLAGS)
*_GCCNOLTO_AARCH64_ASLDLINK_FLAGS   = DEF(GCC49_AARCH64_ASLDLINK_FLAGS)
*_GCCNOLTO_AARCH64_ASM_FLAGS        = DEF(GCC49_AARCH64_ASM_FLAGS)
*_GCCNOLTO_AARCH64_DLINK2_FLAGS     = DEF(GCC49_AARCH64_DLINK2_FLAGS)
*_GCCNOLTO_AARCH64_DTCPP_FLAGS      = DEF(GCC_DTCPP_FLAGS)
*_GCCNOLTO_AARCH64_PLATFORM_FLAGS   =
*_GCCNOLTO_AARCH64_PP_FLAGS         = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCCNOLTO_AARCH64_RC_FLAGS         = DEF(GCC_AARCH64_RC_FLAGS)
*_GCCNOLTO_AARCH64_VFRPP_FLAGS      = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCCNOLTO_AARCH64_CC_XIPFLAGS      = DEF(GCC49_AARCH64_CC_XIPFLAGS)

  DEBUG_GCCNOLTO_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -O0
  DEBUG_GCCNOLTO_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS)
  DEBUG_GCCNOLTO_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

RELEASE_GCCNOLTO_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCCNOLTO_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS)
RELEASE_GCCNOLTO_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

  NOOPT_GCCNOLTO_AARCH64_CC_FLAGS     = DEF(GCC49_AARCH64_CC_FLAGS) -O0
  NOOPT_GCCNOLTO_AARCH64_DLINK_FLAGS  = DEF(GCC49_AARCH64_DLINK_FLAGS) -O0
  NOOPT_GCCNOLTO_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20 -O0

####################################################################################
#
# GCC 5 - This configuration is used to compile under Linux to produce
#         PE/COFF binaries using GCC 5
#
####################################################################################
*_GCC5_*_*_FAMILY                = GCC

*_GCC5_*_MAKE_PATH               = DEF(GCC_HOST_PREFIX)make
*_GCC5_*_*_DLL                   = ENV(GCC5_DLL)
*_GCC5_*_ASL_PATH                = DEF(UNIX_IASL_BIN)

*_GCC5_*_PP_FLAGS                = DEF(GCC_PP_FLAGS)
*_GCC5_*_ASLPP_FLAGS             = DEF(GCC_ASLPP_FLAGS)
*_GCC5_*_ASLCC_FLAGS             = DEF(GCC_ASLCC_FLAGS)
*_GCC5_*_VFRPP_FLAGS             = DEF(GCC_VFRPP_FLAGS)
*_GCC5_*_APP_FLAGS               =
*_GCC5_*_ASL_FLAGS               = DEF(IASL_FLAGS)
*_GCC5_*_ASL_OUTFLAGS            = DEF(IASL_OUTFLAGS)
*_GCC5_*_DEPS_FLAGS                   = DEF(GCC_DEPS_FLAGS)

##################
# GCC5 IA32 definitions
##################
*_GCC5_IA32_OBJCOPY_PATH         = DEF(GCC5_IA32_PREFIX)objcopy
*_GCC5_IA32_CC_PATH              = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_SLINK_PATH           = DEF(GCC5_IA32_PREFIX)gcc-ar
*_GCC5_IA32_DLINK_PATH           = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_ASLDLINK_PATH        = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_ASM_PATH             = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_PP_PATH              = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_VFRPP_PATH           = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_ASLCC_PATH           = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_ASLPP_PATH           = DEF(GCC5_IA32_PREFIX)gcc
*_GCC5_IA32_RC_PATH              = DEF(GCC5_IA32_PREFIX)objcopy

*_GCC5_IA32_ASLCC_FLAGS          = DEF(GCC5_ASLCC_FLAGS) -m32
*_GCC5_IA32_ASLDLINK_FLAGS       = DEF(GCC5_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386 -no-pie
*_GCC5_IA32_ASM_FLAGS            = DEF(GCC5_ASM_FLAGS) -m32 -march=i386
*_GCC5_IA32_DLINK2_FLAGS         = DEF(GCC5_IA32_DLINK2_FLAGS) -no-pie
*_GCC5_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_GCC5_IA32_OBJCOPY_FLAGS        =
*_GCC5_IA32_NASM_FLAGS           = -f elf32

  DEBUG_GCC5_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -flto
  DEBUG_GCC5_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -flto -Os -Wl,-m,elf_i386,--oformat=elf32-i386

RELEASE_GCC5_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC5_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -flto -Os -Wl,-m,elf_i386,--oformat=elf32-i386

  NOOPT_GCC5_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -O0
  NOOPT_GCC5_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -Wl,-m,elf_i386,--oformat=elf32-i386 -O0

##################
# GCC5 X64 definitions
##################
*_GCC5_X64_OBJCOPY_PATH          = DEF(GCC5_X64_PREFIX)objcopy
*_GCC5_X64_CC_PATH               = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_SLINK_PATH            = DEF(GCC5_X64_PREFIX)gcc-ar
*_GCC5_X64_DLINK_PATH            = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_ASLDLINK_PATH         = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_ASM_PATH              = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_PP_PATH               = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_VFRPP_PATH            = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_ASLCC_PATH            = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_ASLPP_PATH            = DEF(GCC5_X64_PREFIX)gcc
*_GCC5_X64_RC_PATH               = DEF(GCC5_X64_PREFIX)objcopy

*_GCC5_X64_ASLCC_FLAGS           = DEF(GCC5_ASLCC_FLAGS) -m64
*_GCC5_X64_ASLDLINK_FLAGS        = DEF(GCC5_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64
*_GCC5_X64_ASM_FLAGS             = DEF(GCC5_ASM_FLAGS) -m64
*_GCC5_X64_DLINK2_FLAGS          = DEF(GCC5_X64_DLINK2_FLAGS)
*_GCC5_X64_RC_FLAGS              = DEF(GCC_X64_RC_FLAGS)
*_GCC5_X64_OBJCOPY_FLAGS         =
*_GCC5_X64_NASM_FLAGS            = -f elf64

  DEBUG_GCC5_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -flto -DUSING_LTO
  DEBUG_GCC5_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -flto -Os

RELEASE_GCC5_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -flto -DUSING_LTO -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC5_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -flto -Os

  NOOPT_GCC5_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -O0
  NOOPT_GCC5_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -O0

##################
# GCC5 ARM definitions
##################
*_GCC5_ARM_CC_PATH               = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_SLINK_PATH            = ENV(GCC5_ARM_PREFIX)gcc-ar
*_GCC5_ARM_DLINK_PATH            = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_ASLDLINK_PATH         = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_ASM_PATH              = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_PP_PATH               = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_VFRPP_PATH            = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_ASLCC_PATH            = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_ASLPP_PATH            = ENV(GCC5_ARM_PREFIX)gcc
*_GCC5_ARM_RC_PATH               = ENV(GCC5_ARM_PREFIX)objcopy

*_GCC5_ARM_ASLCC_FLAGS           = DEF(GCC5_ASLCC_FLAGS)
*_GCC5_ARM_ASLDLINK_FLAGS        = DEF(GCC5_ARM_ASLDLINK_FLAGS)
*_GCC5_ARM_ASM_FLAGS             = DEF(GCC5_ARM_ASM_FLAGS)
*_GCC5_ARM_DLINK2_FLAGS          = DEF(GCC5_ARM_DLINK2_FLAGS)
*_GCC5_ARM_DTCPP_FLAGS           = DEF(GCC_DTCPP_FLAGS)
*_GCC5_ARM_PLATFORM_FLAGS        = -march=armv7-a -mfloat-abi=soft
*_GCC5_ARM_PP_FLAGS              = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC5_ARM_RC_FLAGS              = DEF(GCC_ARM_RC_FLAGS)
*_GCC5_ARM_VFRPP_FLAGS           = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC5_ARM_CC_XIPFLAGS           = DEF(GCC5_ARM_CC_XIPFLAGS)

  DEBUG_GCC5_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
  DEBUG_GCC5_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm

RELEASE_GCC5_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC5_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm

  NOOPT_GCC5_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -O0
  NOOPT_GCC5_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -O0

##################
# GCC5 AARCH64 definitions
##################
*_GCC5_AARCH64_CC_PATH           = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_SLINK_PATH        = ENV(GCC5_AARCH64_PREFIX)gcc-ar
*_GCC5_AARCH64_DLINK_PATH        = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_ASLDLINK_PATH     = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_ASM_PATH          = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_PP_PATH           = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_VFRPP_PATH        = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_ASLCC_PATH        = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_ASLPP_PATH        = ENV(GCC5_AARCH64_PREFIX)gcc
*_GCC5_AARCH64_RC_PATH           = ENV(GCC5_AARCH64_PREFIX)objcopy

*_GCC5_AARCH64_ASLCC_FLAGS       = DEF(GCC5_ASLCC_FLAGS)
*_GCC5_AARCH64_ASLDLINK_FLAGS    = DEF(GCC5_AARCH64_ASLDLINK_FLAGS)
*_GCC5_AARCH64_ASM_FLAGS         = DEF(GCC5_AARCH64_ASM_FLAGS)
*_GCC5_AARCH64_DLINK2_FLAGS      = DEF(GCC5_AARCH64_DLINK2_FLAGS)
*_GCC5_AARCH64_DTCPP_FLAGS       = DEF(GCC_DTCPP_FLAGS)
*_GCC5_AARCH64_PLATFORM_FLAGS    =
*_GCC5_AARCH64_PP_FLAGS          = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC5_AARCH64_RC_FLAGS          = DEF(GCC_AARCH64_RC_FLAGS) DEF(GCC_AARCH64_RC_BTI_FLAGS)
*_GCC5_AARCH64_VFRPP_FLAGS       = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC5_AARCH64_CC_XIPFLAGS       = DEF(GCC5_AARCH64_CC_XIPFLAGS)

  DEBUG_GCC5_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
  DEBUG_GCC5_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wno-lto-type-mismatch
  DEBUG_GCC5_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

RELEASE_GCC5_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC5_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wno-lto-type-mismatch
RELEASE_GCC5_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

  NOOPT_GCC5_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -O0
  NOOPT_GCC5_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -O0
  NOOPT_GCC5_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20 -O0

####################################################################################
#
# GCC RISC-V This configuration is used to compile under Linux to produce
#             PE/COFF binaries using GCC RISC-V tool chain
#
####################################################################################

##################
# GCC5 RISCV64 definitions
##################
*_GCC5_RISCV64_OBJCOPY_PATH         = ENV(GCC5_RISCV64_PREFIX)objcopy
*_GCC5_RISCV64_CC_PATH              = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_SLINK_PATH           = ENV(GCC5_RISCV64_PREFIX)gcc-ar
*_GCC5_RISCV64_DLINK_PATH           = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_ASLDLINK_PATH        = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_ASM_PATH             = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_PP_PATH              = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_VFRPP_PATH           = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_ASLCC_PATH           = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_ASLPP_PATH           = ENV(GCC5_RISCV64_PREFIX)gcc
*_GCC5_RISCV64_RC_PATH              = ENV(GCC5_RISCV64_PREFIX)objcopy

*_GCC5_RISCV64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS)
*_GCC5_RISCV64_ASLDLINK_FLAGS       = DEF(GCC5_RISCV32_RISCV64_ASLDLINK_FLAGS)
*_GCC5_RISCV64_ASM_FLAGS            = DEF(GCC5_RISCV64_ASM_FLAGS)
*_GCC5_RISCV64_CC_FLAGS             = DEF(GCC5_RISCV64_CC_FLAGS) -save-temps
*_GCC5_RISCV64_DLINK_FLAGS          = DEF(GCC5_RISCV64_DLINK_FLAGS)
*_GCC5_RISCV64_DLINK2_FLAGS         = DEF(GCC5_RISCV64_DLINK2_FLAGS)
*_GCC5_RISCV64_RC_FLAGS             = DEF(GCC_RISCV64_RC_FLAGS)
*_GCC5_RISCV64_OBJCOPY_FLAGS        =
*_GCC5_RISCV64_DTCPP_FLAGS          = DEF(GCC_DTCPP_FLAGS)
*_GCC5_RISCV64_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(GCC5_RISCV_OPENSBI_TYPES)

##################
# GCC5 LOONGARCH64 definitions
##################
*_GCC5_LOONGARCH64_OBJCOPY_PATH         = ENV(GCC5_LOONGARCH64_PREFIX)objcopy
*_GCC5_LOONGARCH64_CC_PATH              = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_SLINK_PATH           = ENV(GCC5_LOONGARCH64_PREFIX)gcc-ar
*_GCC5_LOONGARCH64_DLINK_PATH           = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_ASLDLINK_PATH        = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_ASM_PATH             = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_PP_PATH              = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_VFRPP_PATH           = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_ASLCC_PATH           = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_ASLPP_PATH           = ENV(GCC5_LOONGARCH64_PREFIX)gcc
*_GCC5_LOONGARCH64_RC_PATH              = ENV(GCC5_LOONGARCH64_PREFIX)objcopy

*_GCC5_LOONGARCH64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS)
*_GCC5_LOONGARCH64_ASLDLINK_FLAGS       = DEF(GCC5_LOONGARCH64_ASLDLINK_FLAGS)
*_GCC5_LOONGARCH64_ASM_FLAGS            = DEF(GCC5_LOONGARCH64_ASM_FLAGS)
*_GCC5_LOONGARCH64_DLINK_FLAGS          = DEF(GCC5_LOONGARCH64_DLINK_FLAGS)
*_GCC5_LOONGARCH64_DLINK2_FLAGS         = DEF(GCC5_LOONGARCH64_DLINK2_FLAGS)
*_GCC5_LOONGARCH64_RC_FLAGS             = DEF(GCC_LOONGARCH64_RC_FLAGS)
*_GCC5_LOONGARCH64_OBJCOPY_FLAGS        =
*_GCC5_LOONGARCH64_NASM_FLAGS           = -f elf32
*_GCC5_LOONGARCH64_PP_FLAGS             = DEF(GCC5_LOONGARCH64_PP_FLAGS)

DEBUG_GCC5_LOONGARCH64_CC_FLAGS         = DEF(GCC5_LOONGARCH64_CC_FLAGS)
RELEASE_GCC5_LOONGARCH64_CC_FLAGS       = DEF(GCC5_LOONGARCH64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-variable

####################################################################################
#
# GCC - This configuration is used to compile under Linux to produce
#       PE/COFF binaries using GCC 5 or newer
#
####################################################################################
*_GCC_*_*_FAMILY                = GCC

*_GCC_*_MAKE_PATH               = DEF(GCC_HOST_PREFIX)make
*_GCC_*_*_DLL                   = ENV(GCC_DLL)
*_GCC_*_ASL_PATH                = DEF(UNIX_IASL_BIN)

*_GCC_*_PP_FLAGS                = DEF(GCC_PP_FLAGS)
*_GCC_*_ASLPP_FLAGS             = DEF(GCC_ASLPP_FLAGS)
*_GCC_*_ASLCC_FLAGS             = DEF(GCC_ASLCC_FLAGS)
*_GCC_*_VFRPP_FLAGS             = DEF(GCC_VFRPP_FLAGS)
*_GCC_*_APP_FLAGS               =
*_GCC_*_ASL_FLAGS               = DEF(IASL_FLAGS)
*_GCC_*_ASL_OUTFLAGS            = DEF(IASL_OUTFLAGS)
*_GCC_*_DEPS_FLAGS                   = DEF(GCC_DEPS_FLAGS)

##################
# GCC IA32 definitions
##################
*_GCC_IA32_OBJCOPY_PATH         = DEF(GCC_IA32_PREFIX)objcopy
*_GCC_IA32_CC_PATH              = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_SLINK_PATH           = DEF(GCC_IA32_PREFIX)gcc-ar
*_GCC_IA32_DLINK_PATH           = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_ASLDLINK_PATH        = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_ASM_PATH             = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_PP_PATH              = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_VFRPP_PATH           = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_ASLCC_PATH           = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_ASLPP_PATH           = DEF(GCC_IA32_PREFIX)gcc
*_GCC_IA32_RC_PATH              = DEF(GCC_IA32_PREFIX)objcopy

*_GCC_IA32_ASLCC_FLAGS          = DEF(GCC5_ASLCC_FLAGS) -m32
*_GCC_IA32_ASLDLINK_FLAGS       = DEF(GCC5_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386 -no-pie
*_GCC_IA32_ASM_FLAGS            = DEF(GCC5_ASM_FLAGS) -m32 -march=i386
*_GCC_IA32_DLINK2_FLAGS         = DEF(GCC5_IA32_DLINK2_FLAGS) -no-pie
*_GCC_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_GCC_IA32_OBJCOPY_FLAGS        =
*_GCC_IA32_NASM_FLAGS           = -f elf32

  DEBUG_GCC_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -flto
  DEBUG_GCC_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -flto -Os -Wl,-m,elf_i386,--oformat=elf32-i386

RELEASE_GCC_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -flto -Os -Wl,-m,elf_i386,--oformat=elf32-i386

  NOOPT_GCC_IA32_CC_FLAGS       = DEF(GCC5_IA32_CC_FLAGS) -O0
  NOOPT_GCC_IA32_DLINK_FLAGS    = DEF(GCC5_IA32_X64_DLINK_FLAGS) -Wl,-m,elf_i386,--oformat=elf32-i386 -O0

##################
# GCC X64 definitions
##################
*_GCC_X64_OBJCOPY_PATH          = DEF(GCC_X64_PREFIX)objcopy
*_GCC_X64_CC_PATH               = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_SLINK_PATH            = DEF(GCC_X64_PREFIX)gcc-ar
*_GCC_X64_DLINK_PATH            = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_ASLDLINK_PATH         = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_ASM_PATH              = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_PP_PATH               = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_VFRPP_PATH            = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_ASLCC_PATH            = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_ASLPP_PATH            = DEF(GCC_X64_PREFIX)gcc
*_GCC_X64_RC_PATH               = DEF(GCC_X64_PREFIX)objcopy

*_GCC_X64_ASLCC_FLAGS           = DEF(GCC5_ASLCC_FLAGS) -m64
*_GCC_X64_ASLDLINK_FLAGS        = DEF(GCC5_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64
*_GCC_X64_ASM_FLAGS             = DEF(GCC5_ASM_FLAGS) -m64
*_GCC_X64_DLINK2_FLAGS          = DEF(GCC5_X64_DLINK2_FLAGS)
*_GCC_X64_RC_FLAGS              = DEF(GCC_X64_RC_FLAGS)
*_GCC_X64_OBJCOPY_FLAGS         =
*_GCC_X64_NASM_FLAGS            = -f elf64

  DEBUG_GCC_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -flto -DUSING_LTO
  DEBUG_GCC_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -flto -Os

RELEASE_GCC_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -flto -DUSING_LTO -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -flto -Os

  NOOPT_GCC_X64_CC_FLAGS        = DEF(GCC5_X64_CC_FLAGS) -O0
  NOOPT_GCC_X64_DLINK_FLAGS     = DEF(GCC5_X64_DLINK_FLAGS) -O0

##################
# GCC ARM definitions
##################
*_GCC_ARM_CC_PATH               = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_SLINK_PATH            = ENV(GCC_ARM_PREFIX)gcc-ar
*_GCC_ARM_DLINK_PATH            = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_ASLDLINK_PATH         = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_ASM_PATH              = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_PP_PATH               = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_VFRPP_PATH            = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_ASLCC_PATH            = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_ASLPP_PATH            = ENV(GCC_ARM_PREFIX)gcc
*_GCC_ARM_RC_PATH               = ENV(GCC_ARM_PREFIX)objcopy

*_GCC_ARM_ASLCC_FLAGS           = DEF(GCC5_ASLCC_FLAGS)
*_GCC_ARM_ASLDLINK_FLAGS        = DEF(GCC5_ARM_ASLDLINK_FLAGS)
*_GCC_ARM_ASM_FLAGS             = DEF(GCC5_ARM_ASM_FLAGS)
*_GCC_ARM_DLINK2_FLAGS          = DEF(GCC5_ARM_DLINK2_FLAGS)
*_GCC_ARM_DTCPP_FLAGS           = DEF(GCC_DTCPP_FLAGS)
*_GCC_ARM_PLATFORM_FLAGS        = -march=armv7-a -mfloat-abi=soft
*_GCC_ARM_PP_FLAGS              = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC_ARM_RC_FLAGS              = DEF(GCC_ARM_RC_FLAGS)
*_GCC_ARM_VFRPP_FLAGS           = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC_ARM_CC_XIPFLAGS           = DEF(GCC5_ARM_CC_XIPFLAGS)

  DEBUG_GCC_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
  DEBUG_GCC_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm

RELEASE_GCC_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm

  NOOPT_GCC_ARM_CC_FLAGS        = DEF(GCC5_ARM_CC_FLAGS) -O0
  NOOPT_GCC_ARM_DLINK_FLAGS     = DEF(GCC5_ARM_DLINK_FLAGS) -O0

##################
# GCC AARCH64 definitions
##################
*_GCC_AARCH64_CC_PATH           = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_SLINK_PATH        = ENV(GCC_AARCH64_PREFIX)gcc-ar
*_GCC_AARCH64_DLINK_PATH        = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_ASLDLINK_PATH     = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_ASM_PATH          = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_PP_PATH           = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_VFRPP_PATH        = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_ASLCC_PATH        = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_ASLPP_PATH        = ENV(GCC_AARCH64_PREFIX)gcc
*_GCC_AARCH64_RC_PATH           = ENV(GCC_AARCH64_PREFIX)objcopy

*_GCC_AARCH64_ASLCC_FLAGS       = DEF(GCC5_ASLCC_FLAGS)
*_GCC_AARCH64_ASLDLINK_FLAGS    = DEF(GCC5_AARCH64_ASLDLINK_FLAGS)
*_GCC_AARCH64_ASM_FLAGS         = DEF(GCC5_AARCH64_ASM_FLAGS)
*_GCC_AARCH64_DLINK2_FLAGS      = DEF(GCC5_AARCH64_DLINK2_FLAGS)
*_GCC_AARCH64_DTCPP_FLAGS       = DEF(GCC_DTCPP_FLAGS)
*_GCC_AARCH64_PLATFORM_FLAGS    =
*_GCC_AARCH64_PP_FLAGS          = $(PLATFORM_FLAGS) DEF(GCC_PP_FLAGS)
*_GCC_AARCH64_RC_FLAGS          = DEF(GCC_AARCH64_RC_FLAGS) DEF(GCC_AARCH64_RC_BTI_FLAGS)
*_GCC_AARCH64_VFRPP_FLAGS       = $(PLATFORM_FLAGS) DEF(GCC_VFRPP_FLAGS)
*_GCC_AARCH64_CC_XIPFLAGS       = DEF(GCC5_AARCH64_CC_XIPFLAGS)

  DEBUG_GCC_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
  DEBUG_GCC_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wno-lto-type-mismatch
  DEBUG_GCC_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

RELEASE_GCC_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -flto -Wno-unused-but-set-variable -Wno-unused-const-variable
RELEASE_GCC_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -flto -Os -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wno-lto-type-mismatch
RELEASE_GCC_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20

  NOOPT_GCC_AARCH64_CC_FLAGS    = DEF(GCC5_AARCH64_CC_FLAGS) -O0
  NOOPT_GCC_AARCH64_DLINK_FLAGS = DEF(GCC5_AARCH64_DLINK_FLAGS) -O0
  NOOPT_GCC_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20 -O0

####################################################################################
#
# GCC RISC-V This configuration is used to compile under Linux to produce
#             PE/COFF binaries using GCC RISC-V tool chain
#
####################################################################################

##################
# GCC RISCV64 definitions
##################
*_GCC_RISCV64_OBJCOPY_PATH         = ENV(GCC_RISCV64_PREFIX)objcopy
*_GCC_RISCV64_CC_PATH              = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_SLINK_PATH           = ENV(GCC_RISCV64_PREFIX)gcc-ar
*_GCC_RISCV64_DLINK_PATH           = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_ASLDLINK_PATH        = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_ASM_PATH             = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_PP_PATH              = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_VFRPP_PATH           = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_ASLCC_PATH           = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_ASLPP_PATH           = ENV(GCC_RISCV64_PREFIX)gcc
*_GCC_RISCV64_RC_PATH              = ENV(GCC_RISCV64_PREFIX)objcopy

*_GCC_RISCV64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS)
*_GCC_RISCV64_ASLDLINK_FLAGS       = DEF(GCC5_RISCV32_RISCV64_ASLDLINK_FLAGS)
*_GCC_RISCV64_ASM_FLAGS            = DEF(GCC5_RISCV64_ASM_FLAGS)
*_GCC_RISCV64_CC_FLAGS             = DEF(GCC5_RISCV64_CC_FLAGS) -save-temps
*_GCC_RISCV64_DLINK_FLAGS          = DEF(GCC5_RISCV64_DLINK_FLAGS)
*_GCC_RISCV64_DLINK2_FLAGS         = DEF(GCC5_RISCV64_DLINK2_FLAGS)
*_GCC_RISCV64_RC_FLAGS             = DEF(GCC_RISCV64_RC_FLAGS)
*_GCC_RISCV64_OBJCOPY_FLAGS        =
*_GCC_RISCV64_DTCPP_FLAGS          = DEF(GCC_DTCPP_FLAGS)
*_GCC_RISCV64_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(GCC5_RISCV_OPENSBI_TYPES)

##################
# GCC LOONGARCH64 definitions
##################
*_GCC_LOONGARCH64_OBJCOPY_PATH         = ENV(GCC_LOONGARCH64_PREFIX)objcopy
*_GCC_LOONGARCH64_CC_PATH              = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_SLINK_PATH           = ENV(GCC_LOONGARCH64_PREFIX)gcc-ar
*_GCC_LOONGARCH64_DLINK_PATH           = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_ASLDLINK_PATH        = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_ASM_PATH             = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_PP_PATH              = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_VFRPP_PATH           = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_ASLCC_PATH           = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_ASLPP_PATH           = ENV(GCC_LOONGARCH64_PREFIX)gcc
*_GCC_LOONGARCH64_RC_PATH              = ENV(GCC_LOONGARCH64_PREFIX)objcopy

*_GCC_LOONGARCH64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS)
*_GCC_LOONGARCH64_ASLDLINK_FLAGS       = DEF(GCC5_LOONGARCH64_ASLDLINK_FLAGS)
*_GCC_LOONGARCH64_ASM_FLAGS            = DEF(GCC5_LOONGARCH64_ASM_FLAGS)
*_GCC_LOONGARCH64_DLINK_FLAGS          = DEF(GCC5_LOONGARCH64_DLINK_FLAGS)
*_GCC_LOONGARCH64_DLINK2_FLAGS         = DEF(GCC5_LOONGARCH64_DLINK2_FLAGS)
*_GCC_LOONGARCH64_RC_FLAGS             = DEF(GCC_LOONGARCH64_RC_FLAGS)
*_GCC_LOONGARCH64_OBJCOPY_FLAGS        =
*_GCC_LOONGARCH64_NASM_FLAGS           = -f elf32
*_GCC_LOONGARCH64_PP_FLAGS             = DEF(GCC5_LOONGARCH64_PP_FLAGS)

DEBUG_GCC_LOONGARCH64_CC_FLAGS         = DEF(GCC5_LOONGARCH64_CC_FLAGS)
RELEASE_GCC_LOONGARCH64_CC_FLAGS       = DEF(GCC5_LOONGARCH64_CC_FLAGS) -Wno-unused-but-set-variable -Wno-unused-variable

####################################################################################
#
# CLANGPDB - This configuration is used to compile under Windows/Linux/Mac to produce
#  PE/COFF binaries using LLVM/Clang/LLD with Link Time Optimization enabled
#
####################################################################################
*_CLANGPDB_*_*_FAMILY                = GCC
*_CLANGPDB_*_*_BUILDRULEFAMILY       = CLANGPDB
*_CLANGPDB_*_MAKE_PATH               = ENV(CLANG_HOST_BIN)make
*_CLANGPDB_*_*_DLL                   = ENV(CLANGPDB_DLL)
*_CLANGPDB_*_ASL_PATH                = DEF(UNIX_IASL_BIN)

*_CLANGPDB_*_APP_FLAGS               =
*_CLANGPDB_*_ASL_FLAGS               = DEF(DEFAULT_WIN_ASL_FLAGS)
*_CLANGPDB_*_ASL_OUTFLAGS            = DEF(DEFAULT_WIN_ASL_OUTFLAGS)
*_CLANGPDB_*_ASLDLINK_FLAGS          = DEF(MSFT_ASLDLINK_FLAGS)
*_CLANGPDB_*_DEPS_FLAGS              = DEF(GCC_DEPS_FLAGS)

DEFINE CLANGPDB_IA32_PREFIX          = ENV(CLANG_BIN)
DEFINE CLANGPDB_X64_PREFIX           = ENV(CLANG_BIN)

DEFINE CLANGPDB_IA32_TARGET          = -target i686-unknown-windows-gnu
DEFINE CLANGPDB_X64_TARGET           = -target x86_64-unknown-windows-gnu

DEFINE CLANGPDB_WARNING_OVERRIDES    = -Wno-parentheses-equality -Wno-tautological-compare -Wno-tautological-constant-out-of-range-compare -Wno-empty-body -Wno-unused-const-variable -Wno-varargs -Wno-unknown-warning-option -Wno-unused-but-set-variable -Wno-unused-const-variable -Wno-unaligned-access -Wno-microsoft-enum-forward-reference
DEFINE CLANGPDB_ALL_CC_FLAGS         = DEF(GCC48_ALL_CC_FLAGS) DEF(CLANGPDB_WARNING_OVERRIDES) -fno-stack-protector -funsigned-char -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang -Wno-address -Wno-shift-negative-value -Wno-unknown-pragmas -Wno-incompatible-library-redeclaration -Wno-null-dereference -mno-implicit-float -mms-bitfields -mno-stack-arg-probe -nostdlib -nostdlibinc -fseh-exceptions

###########################
# CLANGPDB IA32 definitions
###########################
*_CLANGPDB_IA32_CC_PATH              = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_SLINK_PATH           = DEF(CLANGPDB_IA32_PREFIX)llvm-lib
*_CLANGPDB_IA32_DLINK_PATH           = DEF(CLANGPDB_IA32_PREFIX)lld-link
*_CLANGPDB_IA32_ASLDLINK_PATH        = DEF(CLANGPDB_IA32_PREFIX)lld-link
*_CLANGPDB_IA32_ASM_PATH             = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_PP_PATH              = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_VFRPP_PATH           = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_ASLCC_PATH           = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_ASLPP_PATH           = DEF(CLANGPDB_IA32_PREFIX)clang
*_CLANGPDB_IA32_RC_PATH              = DEF(CLANGPDB_IA32_PREFIX)llvm-rc

*_CLANGPDB_IA32_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS) -m32 -fno-lto DEF(CLANGPDB_IA32_TARGET)
*_CLANGPDB_IA32_ASM_FLAGS            = DEF(GCC_ASM_FLAGS) -m32 -march=i386 DEF(CLANGPDB_IA32_TARGET)
*_CLANGPDB_IA32_OBJCOPY_FLAGS        =
*_CLANGPDB_IA32_NASM_FLAGS           = -f win32
*_CLANGPDB_IA32_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(CLANGPDB_IA32_TARGET)
*_CLANGPDB_IA32_ASLPP_FLAGS          = DEF(GCC_ASLPP_FLAGS) DEF(CLANGPDB_IA32_TARGET)
*_CLANGPDB_IA32_VFRPP_FLAGS          = DEF(GCC_VFRPP_FLAGS) DEF(CLANGPDB_IA32_TARGET)

DEBUG_CLANGPDB_IA32_CC_FLAGS         = DEF(CLANGPDB_ALL_CC_FLAGS) -m32 -Oz -flto -march=i586 DEF(CLANGPDB_IA32_TARGET) -gcodeview -malign-double
DEBUG_CLANGPDB_IA32_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DEBUG:GHASH /MLLVM:-exception-model=wineh /lldmap
DEBUG_CLANGPDB_IA32_DLINK2_FLAGS     =

RELEASE_CLANGPDB_IA32_CC_FLAGS       = DEF(CLANGPDB_ALL_CC_FLAGS) -m32 -Oz -flto -march=i586 DEF(CLANGPDB_IA32_TARGET) -malign-double
RELEASE_CLANGPDB_IA32_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /MERGE:.rdata=.data /MLLVM:-exception-model=wineh /lldmap
RELEASE_CLANGPDB_IA32_DLINK2_FLAGS   =

NOOPT_CLANGPDB_IA32_CC_FLAGS         = DEF(CLANGPDB_ALL_CC_FLAGS) -m32 -O0 -march=i586 DEF(CLANGPDB_IA32_TARGET) -gcodeview -malign-double
NOOPT_CLANGPDB_IA32_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /MACHINE:X86 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DEBUG:GHASH /MLLVM:-exception-model=wineh /lldmap
NOOPT_CLANGPDB_IA32_DLINK2_FLAGS     =

##########################
# CLANGWIN X64 definitions
##########################
*_CLANGPDB_X64_CC_PATH              = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_SLINK_PATH           = DEF(CLANGPDB_X64_PREFIX)llvm-lib
*_CLANGPDB_X64_DLINK_PATH           = DEF(CLANGPDB_X64_PREFIX)lld-link
*_CLANGPDB_X64_ASLDLINK_PATH        = DEF(CLANGPDB_X64_PREFIX)lld-link
*_CLANGPDB_X64_ASM_PATH             = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_PP_PATH              = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_VFRPP_PATH           = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_ASLCC_PATH           = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_ASLPP_PATH           = DEF(CLANGPDB_X64_PREFIX)clang
*_CLANGPDB_X64_RC_PATH              = DEF(CLANGPDB_IA32_PREFIX)llvm-rc

*_CLANGPDB_X64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS) -m64 -fno-lto DEF(CLANGPDB_X64_TARGET)
*_CLANGPDB_X64_ASM_FLAGS            = DEF(GCC_ASM_FLAGS) -m64 DEF(CLANGPDB_X64_TARGET)
*_CLANGPDB_X64_OBJCOPY_FLAGS        =
*_CLANGPDB_X64_NASM_FLAGS           = -f win64
*_CLANGPDB_X64_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(CLANGPDB_X64_TARGET)
*_CLANGPDB_X64_ASLPP_FLAGS          = DEF(GCC_ASLPP_FLAGS) DEF(CLANGPDB_X64_TARGET)
*_CLANGPDB_X64_VFRPP_FLAGS          = DEF(GCC_VFRPP_FLAGS) DEF(CLANGPDB_X64_TARGET)

DEBUG_CLANGPDB_X64_CC_FLAGS         = DEF(CLANGPDB_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -Oz -flto DEF(CLANGPDB_X64_TARGET) -gcodeview  -funwind-tables
DEBUG_CLANGPDB_X64_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /Machine:X64 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DEBUG:GHASH /MLLVM:-exception-model=wineh /lldmap
DEBUG_CLANGPDB_X64_DLINK2_FLAGS     =
DEBUG_CLANGPDB_X64_GENFW_FLAGS      = --keepexceptiontable

RELEASE_CLANGPDB_X64_CC_FLAGS       = DEF(CLANGPDB_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -Oz -flto DEF(CLANGPDB_X64_TARGET) -fno-unwind-tables
RELEASE_CLANGPDB_X64_DLINK_FLAGS    = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /IGNORE:4254 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /SECTION:.xdata,D /SECTION:.pdata,D /Machine:X64 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /MERGE:.rdata=.data /MLLVM:-exception-model=wineh /lldmap
RELEASE_CLANGPDB_X64_DLINK2_FLAGS   =
RELEASE_CLANGPDB_X64_GENFW_FLAGS    =

NOOPT_CLANGPDB_X64_CC_FLAGS         = DEF(CLANGPDB_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -O0 DEF(CLANGPDB_X64_TARGET) -gcodeview -funwind-tables
NOOPT_CLANGPDB_X64_DLINK_FLAGS      = /NOLOGO /NODEFAULTLIB /IGNORE:4001 /OPT:REF /OPT:ICF=10 /ALIGN:32 /DRIVER /FILEALIGN:32 /Machine:X64 /DLL /ENTRY:$(IMAGE_ENTRY_POINT) /SUBSYSTEM:EFI_BOOT_SERVICE_DRIVER /SAFESEH:NO /BASE:0 /DEBUG:GHASH /MLLVM:-exception-model=wineh /lldmap
NOOPT_CLANGPDB_X64_DLINK2_FLAGS     =
NOOPT_CLANGPDB_X64_GENFW_FLAGS      = --keepexceptiontable

####################################################################################
#
# CLANGDWARF - This configuration is used to compile under Windows/Linux/Mac to produce
#  ELF binaries using LLVM/Clang/LLD with Link Time Optimization enabled
#
####################################################################################
*_CLANGDWARF_*_*_FAMILY             = GCC
*_CLANGDWARF_*_*_BUILDRULEFAMILY    = CLANGDWARF

*_CLANGDWARF_*_MAKE_PATH            = ENV(CLANG_HOST_BIN)make
*_CLANGDWARF_*_*_DLL                = ENV(CLANGDWARF_DLL)
*_CLANGDWARF_*_ASL_PATH             = DEF(UNIX_IASL_BIN)

*_CLANGDWARF_*_APP_FLAGS            =
*_CLANGDWARF_*_ASL_FLAGS            = DEF(IASL_FLAGS)
*_CLANGDWARF_*_ASL_OUTFLAGS         = DEF(IASL_OUTFLAGS)

DEFINE CLANGDWARF_IA32_PREFIX       = ENV(CLANG_BIN)
DEFINE CLANGDWARF_X64_PREFIX        = ENV(CLANG_BIN)

# LLVM/CLANG doesn't support -n link option. So, it can't share the same IA32_X64_DLINK_COMMON flag.
# LLVM/CLANG doesn't support common page size. So, it can't share the same GccBase.lds script.
DEFINE CLANGDWARF_IA32_X64_DLINK_COMMON   = -nostdlib -Wl,-q,--gc-sections -z common-page-size=0x40
DEFINE CLANGDWARF_DLINK2_FLAGS_COMMON     = -Wl,--script=$(EDK_TOOLS_PATH)/Scripts/GccBase.lds
DEFINE CLANGDWARF_IA32_X64_ASLDLINK_FLAGS = DEF(CLANGDWARF_IA32_X64_DLINK_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0 DEF(CLANGDWARF_DLINK2_FLAGS_COMMON) -Wl,--entry,ReferenceAcpiTable -u ReferenceAcpiTable
DEFINE CLANGDWARF_IA32_X64_DLINK_FLAGS    = DEF(CLANGDWARF_IA32_X64_DLINK_COMMON) -Wl,--entry,$(IMAGE_ENTRY_POINT) -u $(IMAGE_ENTRY_POINT) -Wl,-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map,--whole-archive -Wl,-z,notext
DEFINE CLANGDWARF_IA32_DLINK2_FLAGS       = -Wl,--defsym=PECOFF_HEADER_SIZE=0x220 DEF(CLANGDWARF_DLINK2_FLAGS_COMMON)
DEFINE CLANGDWARF_X64_DLINK2_FLAGS        = -Wl,--defsym=PECOFF_HEADER_SIZE=0x228 DEF(CLANGDWARF_DLINK2_FLAGS_COMMON)

DEFINE CLANGDWARF_IA32_TARGET             = -target i686-pc-linux-gnu
DEFINE CLANGDWARF_X64_TARGET              = -target x86_64-pc-linux-gnu

DEFINE CLANGDWARF_WARNING_OVERRIDES    = -Wno-parentheses-equality -Wno-empty-body -Wno-unused-const-variable -Wno-varargs -Wno-unknown-warning-option -Wno-unused-but-set-variable -Wno-unused-const-variable -Wno-unaligned-access
DEFINE CLANGDWARF_ALL_CC_FLAGS         = DEF(GCC48_ALL_CC_FLAGS) DEF(CLANGDWARF_WARNING_OVERRIDES) -fno-stack-protector -mms-bitfields -Wno-address -Wno-shift-negative-value -Wno-unknown-pragmas -Wno-incompatible-library-redeclaration -fno-asynchronous-unwind-tables -mno-sse -mno-mmx -msoft-float -mno-implicit-float  -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang -funsigned-char -fno-ms-extensions -Wno-null-dereference

###########################
# CLANGDWARF IA32 definitions
###########################
*_CLANGDWARF_IA32_CC_PATH              = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_SLINK_PATH           = DEF(CLANGDWARF_IA32_PREFIX)llvm-ar
*_CLANGDWARF_IA32_DLINK_PATH           = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_ASLDLINK_PATH        = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_ASM_PATH             = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_PP_PATH              = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_VFRPP_PATH           = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_ASLCC_PATH           = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_ASLPP_PATH           = DEF(CLANGDWARF_IA32_PREFIX)clang
*_CLANGDWARF_IA32_RC_PATH              = DEF(CLANGDWARF_IA32_PREFIX)llvm-objcopy

*_CLANGDWARF_IA32_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS) -m32 -fno-lto DEF(CLANGDWARF_IA32_TARGET)
*_CLANGDWARF_IA32_ASLDLINK_FLAGS       = DEF(CLANGDWARF_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_i386 -fuse-ld=lld -no-pie
*_CLANGDWARF_IA32_ASM_FLAGS            = DEF(GCC5_ASM_FLAGS) -m32 -march=i386 DEF(CLANGDWARF_IA32_TARGET)
*_CLANGDWARF_IA32_RC_FLAGS             = DEF(GCC_IA32_RC_FLAGS)
*_CLANGDWARF_IA32_OBJCOPY_FLAGS        =
*_CLANGDWARF_IA32_NASM_FLAGS           = -f elf32
*_CLANGDWARF_IA32_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(CLANGDWARF_IA32_TARGET)
*_CLANGDWARF_IA32_ASLPP_FLAGS          = DEF(GCC_ASLPP_FLAGS) DEF(CLANGDWARF_IA32_TARGET)
*_CLANGDWARF_IA32_VFRPP_FLAGS          = DEF(GCC_VFRPP_FLAGS) DEF(CLANGDWARF_IA32_TARGET)

DEBUG_CLANGDWARF_IA32_CC_FLAGS         = DEF(CLANGDWARF_ALL_CC_FLAGS) -fno-pic -fno-pie -m32 -Oz -flto -march=i586 DEF(CLANGDWARF_IA32_TARGET) -g -malign-double
DEBUG_CLANGDWARF_IA32_DLINK_FLAGS      = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -flto -Wl,-O3 -Wl,-melf_i386 -Wl,--oformat,elf32-i386
DEBUG_CLANGDWARF_IA32_DLINK2_FLAGS     = DEF(CLANGDWARF_IA32_DLINK2_FLAGS) -O3 -fuse-ld=lld -no-pie

RELEASE_CLANGDWARF_IA32_CC_FLAGS       = DEF(CLANGDWARF_ALL_CC_FLAGS) -fno-pic -fno-pie -m32 -Oz -flto -march=i586 DEF(CLANGDWARF_IA32_TARGET) -malign-double
RELEASE_CLANGDWARF_IA32_DLINK_FLAGS    = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -flto -Wl,-O3 -Wl,-melf_i386 -Wl,--oformat,elf32-i386
RELEASE_CLANGDWARF_IA32_DLINK2_FLAGS   = DEF(CLANGDWARF_IA32_DLINK2_FLAGS) -O3 -fuse-ld=lld -no-pie

NOOPT_CLANGDWARF_IA32_CC_FLAGS         = DEF(CLANGDWARF_ALL_CC_FLAGS) -fno-pic -fno-pie -m32 -O0 -march=i586 DEF(CLANGDWARF_IA32_TARGET) -g -malign-double
NOOPT_CLANGDWARF_IA32_DLINK_FLAGS      = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -Wl,-O0 -Wl,-melf_i386 -Wl,--oformat,elf32-i386
NOOPT_CLANGDWARF_IA32_DLINK2_FLAGS     = DEF(CLANGDWARF_IA32_DLINK2_FLAGS) -O0 -fuse-ld=lld -no-pie

##########################
# CLANGDWARF X64 definitions
##########################
*_CLANGDWARF_X64_CC_PATH              = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_SLINK_PATH           = DEF(CLANGDWARF_X64_PREFIX)llvm-ar
*_CLANGDWARF_X64_DLINK_PATH           = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_ASLDLINK_PATH        = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_ASM_PATH             = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_PP_PATH              = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_VFRPP_PATH           = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_ASLCC_PATH           = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_ASLPP_PATH           = DEF(CLANGDWARF_X64_PREFIX)clang
*_CLANGDWARF_X64_RC_PATH              = DEF(CLANGDWARF_X64_PREFIX)llvm-objcopy

*_CLANGDWARF_X64_ASLCC_FLAGS          = DEF(GCC_ASLCC_FLAGS) -m64 -fno-lto DEF(CLANGDWARF_X64_TARGET)
*_CLANGDWARF_X64_ASLDLINK_FLAGS       = DEF(CLANGDWARF_IA32_X64_ASLDLINK_FLAGS) -Wl,-m,elf_x86_64 -fuse-ld=lld
*_CLANGDWARF_X64_ASM_FLAGS            = DEF(GCC5_ASM_FLAGS) -m64 DEF(CLANGDWARF_X64_TARGET)
*_CLANGDWARF_X64_RC_FLAGS             = DEF(GCC_X64_RC_FLAGS)
*_CLANGDWARF_X64_OBJCOPY_FLAGS        =
*_CLANGDWARF_X64_NASM_FLAGS           = -f elf64
*_CLANGDWARF_X64_PP_FLAGS             = DEF(GCC_PP_FLAGS) DEF(CLANGDWARF_X64_TARGET)
*_CLANGDWARF_X64_ASLPP_FLAGS          = DEF(GCC_ASLPP_FLAGS) DEF(CLANGDWARF_X64_TARGET)
*_CLANGDWARF_X64_VFRPP_FLAGS          = DEF(GCC_VFRPP_FLAGS) DEF(CLANGDWARF_X64_TARGET)

DEBUG_CLANGDWARF_X64_CC_FLAGS         = DEF(CLANGDWARF_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -fpie -fdirect-access-external-data -Oz -flto DEF(CLANGDWARF_X64_TARGET) -g
DEBUG_CLANGDWARF_X64_DLINK_FLAGS      = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -flto -Wl,-O3 -Wl,-melf_x86_64 -Wl,--oformat,elf64-x86-64 -Wl,-pie -mcmodel=small -Wl,--apply-dynamic-relocs
DEBUG_CLANGDWARF_X64_DLINK2_FLAGS     = DEF(CLANGDWARF_X64_DLINK2_FLAGS) -O3 -fuse-ld=lld

RELEASE_CLANGDWARF_X64_CC_FLAGS       = DEF(CLANGDWARF_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -fpie -fdirect-access-external-data -Oz -flto DEF(CLANGDWARF_X64_TARGET)
RELEASE_CLANGDWARF_X64_DLINK_FLAGS    = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -flto -Wl,-O3 -Wl,-melf_x86_64 -Wl,--oformat,elf64-x86-64 -Wl,-pie -mcmodel=small -Wl,--apply-dynamic-relocs
RELEASE_CLANGDWARF_X64_DLINK2_FLAGS   = DEF(CLANGDWARF_X64_DLINK2_FLAGS) -O3 -fuse-ld=lld

NOOPT_CLANGDWARF_X64_CC_FLAGS         = DEF(CLANGDWARF_ALL_CC_FLAGS) -m64 "-DEFIAPI=__attribute__((ms_abi))" -mno-red-zone -mcmodel=small -fpie -fdirect-access-external-data -O0 DEF(CLANGDWARF_X64_TARGET) -g
NOOPT_CLANGDWARF_X64_DLINK_FLAGS      = DEF(CLANGDWARF_IA32_X64_DLINK_FLAGS) -Wl,-O0 -Wl,-melf_x86_64 -Wl,--oformat,elf64-x86-64 -Wl,-pie -mcmodel=small -Wl,--apply-dynamic-relocs
NOOPT_CLANGDWARF_X64_DLINK2_FLAGS     = DEF(CLANGDWARF_X64_DLINK2_FLAGS) -O0 -fuse-ld=lld

##################
# CLANGDWARF ARM definitions
##################
DEFINE CLANGDWARF_ARM_TARGET        = -target arm-linux-gnueabi
DEFINE CLANGDWARF_ARM_CC_FLAGS      = DEF(GCC_ARM_CC_FLAGS) DEF(CLANGDWARF_ARM_TARGET) DEF(CLANGDWARF_WARNING_OVERRIDES) -mno-movt -fno-stack-protector
DEFINE CLANGDWARF_ARM_DLINK_FLAGS   = DEF(CLANGDWARF_ARM_TARGET) DEF(GCC_ARM_DLINK_FLAGS)

*_CLANGDWARF_ARM_PP_FLAGS           = DEF(GCC_PP_FLAGS)
*_CLANGDWARF_ARM_ASLCC_FLAGS        = DEF(GCC_ASLCC_FLAGS)
*_CLANGDWARF_ARM_APP_FLAGS          =
*_CLANGDWARF_ARM_ASL_FLAGS          = DEF(IASL_FLAGS)
*_CLANGDWARF_ARM_ASL_OUTFLAGS       = DEF(IASL_OUTFLAGS)
*_CLANGDWARF_ARM_DTCPP_FLAGS        = DEF(GCC_DTCPP_FLAGS)

*_CLANGDWARF_ARM_CC_PATH            = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_ASM_PATH           = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_PP_PATH            = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_VFRPP_PATH         = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_ASLCC_PATH         = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_ASLPP_PATH         = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_DLINK_PATH         = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_ARM_ASLDLINK_PATH      = ENV(CLANGDWARF_BIN)clang

*_CLANGDWARF_ARM_SLINK_PATH         = ENV(CLANGDWARF_BIN)llvm-ar
*_CLANGDWARF_ARM_RC_PATH            = ENV(CLANGDWARF_BIN)llvm-objcopy

*_CLANGDWARF_ARM_ASLCC_FLAGS        = DEF(GCC_ASLCC_FLAGS) -fno-lto
*_CLANGDWARF_ARM_ASLDLINK_FLAGS     = DEF(CLANGDWARF_ARM_TARGET) DEF(GCC_ARM_ASLDLINK_FLAGS)
*_CLANGDWARF_ARM_ASM_FLAGS          = DEF(GCC_ASM_FLAGS) DEF(CLANGDWARF_ARM_TARGET) $(PLATFORM_FLAGS) -Qunused-arguments
*_CLANGDWARF_ARM_DLINK2_FLAGS       = DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x220
*_CLANGDWARF_ARM_PLATFORM_FLAGS     = -march=armv7-a
*_CLANGDWARF_ARM_PP_FLAGS           = DEF(GCC_PP_FLAGS) DEF(CLANGDWARF_ARM_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_ARM_RC_FLAGS           = DEF(GCC_ARM_RC_FLAGS)
*_CLANGDWARF_ARM_VFRPP_FLAGS        = DEF(GCC_VFRPP_FLAGS) DEF(CLANGDWARF_ARM_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_ARM_ASLPP_FLAGS        = DEF(GCC_ASLPP_FLAGS) DEF(CLANGDWARF_ARM_TARGET)
*_CLANGDWARF_ARM_CC_XIPFLAGS        = DEF(GCC_ARM_CC_XIPFLAGS)

  DEBUG_CLANGDWARF_ARM_CC_FLAGS     = DEF(CLANGDWARF_ARM_CC_FLAGS) $(PLATFORM_FLAGS) -flto -O1
  DEBUG_CLANGDWARF_ARM_DLINK_FLAGS  = DEF(CLANGDWARF_ARM_DLINK_FLAGS) -flto -Wl,-O1 -fuse-ld=lld -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm -Wl,--no-pie,--no-relax
  NOOPT_CLANGDWARF_ARM_CC_FLAGS     = DEF(CLANGDWARF_ARM_CC_FLAGS) $(PLATFORM_FLAGS) -O0
  NOOPT_CLANGDWARF_ARM_DLINK_FLAGS  = DEF(CLANGDWARF_ARM_DLINK_FLAGS) -fuse-ld=lld -Wl,--no-pie,--no-relax
RELEASE_CLANGDWARF_ARM_CC_FLAGS     = DEF(CLANGDWARF_ARM_CC_FLAGS) $(PLATFORM_FLAGS) -flto -Oz
RELEASE_CLANGDWARF_ARM_DLINK_FLAGS  = DEF(CLANGDWARF_ARM_DLINK_FLAGS) -flto -Wl,-O3 -fuse-ld=lld -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-arm -Wl,-plugin-opt=-pass-through=-llto-arm -Wl,--no-pie,--no-relax

##################
# CLANGDWARF AARCH64 definitions
##################
DEFINE CLANGDWARF_AARCH64_TARGET    = -target aarch64-linux-gnu
DEFINE CLANGDWARF_AARCH64_CC_FLAGS  = DEF(GCC_AARCH64_CC_FLAGS) DEF(CLANGDWARF_AARCH64_TARGET) -mcmodel=small DEF(CLANGDWARF_WARNING_OVERRIDES)
DEFINE CLANGDWARF_AARCH64_DLINK_FLAGS  = DEF(CLANGDWARF_AARCH64_TARGET) DEF(GCC_AARCH64_DLINK_FLAGS) -z common-page-size=0x1000

*_CLANGDWARF_AARCH64_PP_FLAGS       = DEF(GCC_PP_FLAGS)
*_CLANGDWARF_AARCH64_ASLCC_FLAGS    = DEF(GCC_ASLCC_FLAGS)
*_CLANGDWARF_AARCH64_APP_FLAGS      =
*_CLANGDWARF_AARCH64_ASL_FLAGS      = DEF(IASL_FLAGS)
*_CLANGDWARF_AARCH64_ASL_OUTFLAGS   = DEF(IASL_OUTFLAGS)
*_CLANGDWARF_AARCH64_DTCPP_FLAGS    = DEF(GCC_DTCPP_FLAGS)

*_CLANGDWARF_AARCH64_CC_PATH        = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_ASM_PATH       = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_PP_PATH        = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_VFRPP_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_ASLCC_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_ASLPP_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_DLINK_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_AARCH64_ASLDLINK_PATH  = ENV(CLANGDWARF_BIN)clang

*_CLANGDWARF_AARCH64_SLINK_PATH     = ENV(CLANGDWARF_BIN)llvm-ar
*_CLANGDWARF_AARCH64_RC_PATH        = ENV(CLANGDWARF_BIN)llvm-objcopy

*_CLANGDWARF_AARCH64_ASLCC_FLAGS    = DEF(GCC_ASLCC_FLAGS) -fno-lto
*_CLANGDWARF_AARCH64_ASLDLINK_FLAGS = DEF(CLANGDWARF_AARCH64_TARGET) DEF(GCC_AARCH64_ASLDLINK_FLAGS)
*_CLANGDWARF_AARCH64_ASM_FLAGS      = DEF(GCC_ASM_FLAGS) DEF(CLANGDWARF_AARCH64_TARGET) $(PLATFORM_FLAGS) -Qunused-arguments
*_CLANGDWARF_AARCH64_DLINK_FLAGS    = DEF(CLANGDWARF_AARCH64_TARGET) DEF(GCC_AARCH64_DLINK_FLAGS) -z common-page-size=0x1000
*_CLANGDWARF_AARCH64_DLINK_XIPFLAGS = -z common-page-size=0x20
*_CLANGDWARF_AARCH64_DLINK2_FLAGS   = DEF(GCC_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x228
*_CLANGDWARF_AARCH64_PLATFORM_FLAGS =
*_CLANGDWARF_AARCH64_PP_FLAGS       = DEF(GCC_PP_FLAGS) DEF(CLANGDWARF_AARCH64_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_AARCH64_RC_FLAGS       = DEF(GCC_AARCH64_RC_FLAGS) DEF(GCC_AARCH64_RC_BTI_FLAGS)
*_CLANGDWARF_AARCH64_VFRPP_FLAGS    = DEF(GCC_VFRPP_FLAGS) DEF(CLANGDWARF_AARCH64_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_AARCH64_ASLPP_FLAGS    = DEF(GCC_ASLPP_FLAGS) DEF(CLANGDWARF_AARCH64_TARGET)
*_CLANGDWARF_AARCH64_CC_XIPFLAGS    = -mstrict-align

  DEBUG_CLANGDWARF_AARCH64_CC_FLAGS    = DEF(CLANGDWARF_AARCH64_CC_FLAGS) $(PLATFORM_FLAGS) -flto -O1
  DEBUG_CLANGDWARF_AARCH64_DLINK_FLAGS = DEF(CLANGDWARF_AARCH64_DLINK_FLAGS) -flto -Wl,-O1 -fuse-ld=lld -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wl,--no-pie,--no-relax
  NOOPT_CLANGDWARF_AARCH64_CC_FLAGS    = DEF(CLANGDWARF_AARCH64_CC_FLAGS) $(PLATFORM_FLAGS) -O0
  NOOPT_CLANGDWARF_AARCH64_DLINK_FLAGS = DEF(CLANGDWARF_AARCH64_DLINK_FLAGS) -fuse-ld=lld -Wl,--no-pie,--no-relax
RELEASE_CLANGDWARF_AARCH64_CC_FLAGS    = DEF(CLANGDWARF_AARCH64_CC_FLAGS) $(PLATFORM_FLAGS) -flto -Oz
RELEASE_CLANGDWARF_AARCH64_DLINK_FLAGS = DEF(CLANGDWARF_AARCH64_DLINK_FLAGS) -flto -Wl,-O3 -fuse-ld=lld -L$(WORKSPACE)/BaseTools/Bin/GccLto -llto-aarch64 -Wl,-plugin-opt=-pass-through=-llto-aarch64 -Wl,--no-pie,--no-relax

##################
# CLANGDWARF RISCV64 definitions
##################
DEFINE CLANGDWARF_RISCV64_TARGET    = -target riscv64-linux-gnu
DEFINE CLANGDWARF_RISCV64_CC_COMMON = DEF(GCC5_RISCV_ALL_CC_FLAGS) DEF(GCC5_RISCV_ALL_CC_FLAGS_WARNING_DISABLE) DEF(GCC5_RISCV_OPENSBI_TYPES) -march=DEF(GCC5_RISCV64_ARCH) -fno-builtin -fno-builtin-memcpy -fno-stack-protector -Wno-address -fno-asynchronous-unwind-tables -fno-unwind-tables -Wno-unused-but-set-variable -fpack-struct=8 -mcmodel=medany -mabi=lp64 -mno-relax
DEFINE CLANGDWARF_RISCV64_CC_FLAGS  = DEF(CLANGDWARF_RISCV64_CC_COMMON) DEF(CLANGDWARF_RISCV64_TARGET) DEF(CLANGDWARF_WARNING_OVERRIDES)

# This is similar to GCC flags but without -n
DEFINE CLANGDWARF_RISCV64_ALL_DLINK_COMMON  = -nostdlib -Wl,-q,--gc-sections -z common-page-size=0x40
DEFINE CLANGDWARF_RISCV64_ALL_DLINK_FLAGS   = DEF(CLANGDWARF_RISCV64_ALL_DLINK_COMMON) -Wl,--entry,$(IMAGE_ENTRY_POINT) -u $(IMAGE_ENTRY_POINT) -Wl,-Map,$(DEST_DIR_DEBUG)/$(BASE_NAME).map
DEFINE CLANGDWARF_RISCV64_DLINK_FLAGS       = DEF(CLANGDWARF_RISCV64_TARGET) DEF(CLANGDWARF_RISCV64_ALL_DLINK_FLAGS) -Wl,-melf64lriscv,--oformat=elf64-littleriscv,--no-relax

*_CLANGDWARF_RISCV64_PP_FLAGS       = DEF(GCC_PP_FLAGS)
*_CLANGDWARF_RISCV64_ASLCC_FLAGS    = DEF(GCC_ASLCC_FLAGS)
*_CLANGDWARF_RISCV64_APP_FLAGS      =
*_CLANGDWARF_RISCV64_ASL_FLAGS      = DEF(IASL_FLAGS)
*_CLANGDWARF_RISCV64_ASL_OUTFLAGS   = DEF(IASL_OUTFLAGS)
*_CLANGDWARF_RISCV64_DTCPP_FLAGS    = DEF(GCC_DTCPP_FLAGS)
*_CLANGDWARF_RISCV64_DEPS_FLAGS     = DEF(GCC_DEPS_FLAGS)

*_CLANGDWARF_RISCV64_CC_PATH        = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_ASM_PATH       = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_PP_PATH        = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_VFRPP_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_ASLCC_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_ASLPP_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_DLINK_PATH     = ENV(CLANGDWARF_BIN)clang
*_CLANGDWARF_RISCV64_ASLDLINK_PATH  = ENV(CLANGDWARF_BIN)clang

*_CLANGDWARF_RISCV64_SLINK_PATH     = ENV(CLANGDWARF_BIN)llvm-ar
*_CLANGDWARF_RISCV64_RC_PATH        = ENV(CLANGDWARF_BIN)llvm-objcopy

*_CLANGDWARF_RISCV64_ASLCC_FLAGS    = DEF(GCC_ASLCC_FLAGS) -fno-lto
*_CLANGDWARF_RISCV64_ASLDLINK_FLAGS = DEF(CLANGDWARF_RISCV64_TARGET) DEF(GCC5_RISCV32_RISCV64_ASLDLINK_FLAGS)
*_CLANGDWARF_RISCV64_ASM_FLAGS      = DEF(GCC_ASM_FLAGS) DEF(CLANGDWARF_RISCV64_TARGET) $(PLATFORM_FLAGS) -Qunused-arguments -mabi=lp64 -mno-relax
*_CLANGDWARF_RISCV64_DLINK_FLAGS    = DEF(CLANGDWARF_RISCV64_TARGET) DEF(GCC5_RISCV64_DLINK_FLAGS)
*_CLANGDWARF_RISCV64_DLINK_XIPFLAGS = -z common-page-size=0x20
*_CLANGDWARF_RISCV64_DLINK2_FLAGS   = DEF(CLANGDWARF_DLINK2_FLAGS_COMMON) -Wl,--defsym=PECOFF_HEADER_SIZE=0x240
*_CLANGDWARF_RISCV64_PLATFORM_FLAGS =
*_CLANGDWARF_RISCV64_PP_FLAGS       = DEF(GCC_PP_FLAGS) DEF(CLANGDWARF_RISCV64_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_RISCV64_RC_FLAGS       = DEF(GCC_RISCV64_RC_FLAGS)
*_CLANGDWARF_RISCV64_VFRPP_FLAGS    = DEF(GCC_VFRPP_FLAGS) DEF(CLANGDWARF_RISCV64_TARGET) $(PLATFORM_FLAGS)
*_CLANGDWARF_RISCV64_ASLPP_FLAGS    = DEF(GCC_ASLPP_FLAGS) DEF(CLANGDWARF_RISCV64_TARGET)
*_CLANGDWARF_RISCV64_CC_XIPFLAGS    = DEF(GCC_RISCV64_CC_XIPFLAGS)

  DEBUG_CLANGDWARF_RISCV64_CC_FLAGS    = DEF(CLANGDWARF_RISCV64_CC_FLAGS) $(PLATFORM_FLAGS) -flto -O1
  DEBUG_CLANGDWARF_RISCV64_DLINK_FLAGS = DEF(CLANGDWARF_RISCV64_DLINK_FLAGS) -flto -Wl,-O1 -fuse-ld=lld -Wl,--no-pie,--no-relax
  NOOPT_CLANGDWARF_RISCV64_CC_FLAGS    = DEF(CLANGDWARF_RISCV64_CC_FLAGS) $(PLATFORM_FLAGS) -O0
  NOOPT_CLANGDWARF_RISCV64_DLINK_FLAGS = DEF(CLANGDWARF_RISCV64_DLINK_FLAGS) -fuse-ld=lld -Wl,--no-pie,--no-relax
RELEASE_CLANGDWARF_RISCV64_CC_FLAGS    = DEF(CLANGDWARF_RISCV64_CC_FLAGS) $(PLATFORM_FLAGS) -flto -Oz
RELEASE_CLANGDWARF_RISCV64_DLINK_FLAGS = DEF(CLANGDWARF_RISCV64_DLINK_FLAGS) -flto -Wl,-O3 -fuse-ld=lld -Wl,--no-pie,--no-relax

#
#
# XCODE5 support
#

*_XCODE5_*_*_FAMILY            = GCC
*_XCODE5_*_*_BUILDRULEFAMILY   = XCODE

#
# use xcode-select to change Xcode version of command line tools
#
*_XCODE5_*_MAKE_PATH     = make
*_XCODE5_*_CC_PATH       = clang
*_XCODE5_*_SLINK_PATH    = libtool
*_XCODE5_*_DLINK_PATH    = ld
*_XCODE5_*_ASM_PATH      = as
*_XCODE5_*_PP_PATH       = clang
*_XCODE5_*_VFRPP_PATH    = clang
*_XCODE5_*_ASL_PATH      = iasl
*_XCODE5_*_ASLCC_PATH    = clang
*_XCODE5_*_ASLPP_PATH    = clang
*_XCODE5_*_ASLDLINK_PATH = ld
*_XCODE5_*_DSYMUTIL_PATH = /usr/bin/dsymutil
*_XCODE5_*_MTOC_PATH     = /usr/local/bin/mtoc
*_XCODE5_*_DEPS_FLAGS    = DEF(GCC_DEPS_FLAGS)

##################
# ASL definitions
##################
*_XCODE5_*_ASLCC_FLAGS      = -x c -save-temps -g -O0 -fshort-wchar -fno-strict-aliasing -Wall -Werror -Wno-missing-braces -c -include AutoGen.h
*_XCODE5_*_ASLDLINK_FLAGS   = -e _ReferenceAcpiTable -preload -segalign 0x20  -pie -seg1addr 0x240 -read_only_relocs suppress -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
*_XCODE5_*_ASLPP_FLAGS      = -x c -E -include AutoGen.h
*_XCODE5_*_ASL_FLAGS        =
*_XCODE5_*_ASL_OUTFLAGS     = DEF(IASL_OUTFLAGS)

##################
# MTOC definitions
##################

  DEBUG_XCODE5_*_MTOC_FLAGS = -align 0x20 -d $(DEBUG_DIR)/$(MODULE_NAME).dll
  NOOPT_XCODE5_*_MTOC_FLAGS = -align 0x20 -d $(DEBUG_DIR)/$(MODULE_NAME).dll
RELEASE_XCODE5_*_MTOC_FLAGS = -align 0x20

####################
# IA-32 definitions
####################
  DEBUG_XCODE5_IA32_DLINK_FLAGS      = -arch i386 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -read_only_relocs suppress -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
  NOOPT_XCODE5_IA32_DLINK_FLAGS      = -arch i386 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -read_only_relocs suppress -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
RELEASE_XCODE5_IA32_DLINK_FLAGS      = -arch i386 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -read_only_relocs suppress -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map

*_XCODE5_IA32_SLINK_FLAGS      = -static -o
  DEBUG_XCODE5_IA32_ASM_FLAGS  = -arch i386 -g
  NOOPT_XCODE5_IA32_ASM_FLAGS  = -arch i386 -g
RELEASE_XCODE5_IA32_ASM_FLAGS  = -arch i386
      *_XCODE5_IA32_NASM_FLAGS = -f macho32


  DEBUG_XCODE5_IA32_CC_FLAGS   = -arch i386 -c -g -Os       -Wall -Werror -include AutoGen.h -funsigned-char -fno-stack-protector -fno-builtin -fshort-wchar -fasm-blocks -mdynamic-no-pic -mno-implicit-float -mms-bitfields -msoft-float -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang $(PLATFORM_FLAGS)
RELEASE_XCODE5_IA32_CC_FLAGS   = -arch i386 -c    -Os       -Wall -Werror -include AutoGen.h -funsigned-char -fno-stack-protector -fno-builtin -fshort-wchar -fasm-blocks -mdynamic-no-pic -mno-implicit-float -mms-bitfields -msoft-float -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -Wno-unused-const-variable -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang $(PLATFORM_FLAGS)
  NOOPT_XCODE5_IA32_CC_FLAGS   = -arch i386 -c -g -O0       -Wall -Werror -include AutoGen.h -funsigned-char -fno-stack-protector -fno-builtin -fshort-wchar -fasm-blocks -mdynamic-no-pic -mno-implicit-float -mms-bitfields -msoft-float -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang $(PLATFORM_FLAGS)

##################
# X64 definitions
##################
  DEBUG_XCODE5_X64_DLINK_FLAGS      = -arch x86_64 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
  NOOPT_XCODE5_X64_DLINK_FLAGS      = -arch x86_64 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map
RELEASE_XCODE5_X64_DLINK_FLAGS      = -arch x86_64 -u _$(IMAGE_ENTRY_POINT) -e _$(IMAGE_ENTRY_POINT) -preload -segalign 0x20  -pie -all_load -dead_strip -seg1addr 0x240 -map $(DEST_DIR_DEBUG)/$(BASE_NAME).map

*_XCODE5_X64_SLINK_FLAGS      = -static -o
  DEBUG_XCODE5_X64_ASM_FLAGS  = -arch x86_64 -g
  NOOPT_XCODE5_X64_ASM_FLAGS  = -arch x86_64 -g
RELEASE_XCODE5_X64_ASM_FLAGS  = -arch x86_64
      *_XCODE5_X64_NASM_FLAGS = -f macho64
*_XCODE5_*_PP_FLAGS         = -E -x assembler-with-cpp -include AutoGen.h
*_XCODE5_*_VFRPP_FLAGS      = -x c -E -P -DVFRCOMPILE -include $(MODULE_NAME)StrDefs.h

  DEBUG_XCODE5_X64_CC_FLAGS   = -target x86_64-pc-win32-macho -c -g -gdwarf -Os       -Wall -Werror -Wextra -include AutoGen.h -funsigned-char -fno-ms-extensions -fno-stack-protector -fno-builtin -fshort-wchar -mno-implicit-float -mms-bitfields -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang -D NO_MSABI_VA_FUNCS $(PLATFORM_FLAGS)
  NOOPT_XCODE5_X64_CC_FLAGS   = -target x86_64-pc-win32-macho -c -g -gdwarf -O0       -Wall -Werror -Wextra -include AutoGen.h -funsigned-char -fno-ms-extensions -fno-stack-protector -fno-builtin -fshort-wchar -mno-implicit-float -mms-bitfields -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang -D NO_MSABI_VA_FUNCS $(PLATFORM_FLAGS)
RELEASE_XCODE5_X64_CC_FLAGS   = -target x86_64-pc-win32-macho -c    -Os       -Wall -Werror -Wextra -include AutoGen.h -funsigned-char -fno-ms-extensions -fno-stack-protector -fno-builtin -fshort-wchar -mno-implicit-float -mms-bitfields -Wno-unused-parameter -Wno-missing-braces -Wno-missing-field-initializers -Wno-tautological-compare -Wno-sign-compare -Wno-varargs -Wno-unused-const-variable -ftrap-function=undefined_behavior_has_been_optimized_away_by_clang -D NO_MSABI_VA_FUNCS $(PLATFORM_FLAGS)

#################
# ASM 16 linker definitions
#################
*_*_*_ASMLINK_PATH                 = DEF(WINDDK_BIN16)\link16.exe
*_*_*_ASMLINK_FLAGS                = /nologo /tiny

##################
# VfrCompiler definitions
##################
*_*_*_VFR_PATH                      = VfrCompile
*_*_*_VFR_FLAGS                     = -l -n

##################
# OptionRom tool definitions
##################
*_*_*_OPTROM_PATH                   = EfiRom
*_*_*_OPTROM_FLAGS                  = -e

##################
# GenFw tool definitions
##################
*_*_*_GENFW_PATH                   = GenFw
*_*_*_GENFW_FLAGS                  =

##################
# Asl Compiler definitions
##################
*_*_*_ASLCC_FLAGS                  = /nologo /c /FIAutoGen.h /TC /Dmain=ReferenceAcpiTable
*_*_*_ASLDLINK_FLAGS               = /NODEFAULTLIB /ENTRY:ReferenceAcpiTable /SUBSYSTEM:CONSOLE
*_*_*_ASLPP_FLAGS                  = /nologo /EP /C
*_*_*_ASL_FLAGS                    =

##################
# GenCrc32 tool definitions
##################
*_*_*_CRC32_PATH          = GenCrc32
*_*_*_CRC32_GUID          = FC1BCDB0-7D31-49AA-936A-A4600D9DD083

##################
# Rsa2048Sha256Sign tool definitions
#
# Notes: This tool definition uses a test signing key for development purposes only.
#        The tool Rsa2048Sha256GenerateKeys can be used to generate a new private/public key
#        and the gEfiSecurityPkgTokenSpaceGuid.PcdRsa2048Sha256PublicKeyBuffer PCD value.
#        A custom tool/script can be implemented using the new private/public key with
#        the Rsa2048Sha256Sign tool and this tool definition can be updated to use a
#        custom tool/script.
#
#   Generate new private/public key and gEfiSecurityPkgTokenSpaceGuid.PcdRsa2048Sha256PublicKeyBuffer PCD value
#
#       Rsa2048Sha256GenerateKeys.py -o MyKey.pem --public-key-hash-c MyKey.pcd
#
#   Custom script example (MyRsa2048Sha256Sign.cmd):
#
#       Rsa2048Sha256Sign --private-key MyKey.pem %1 %2 %3 %4 %5 %6 %7 %8 %9
#
#   WARNING: Vendors that uses private keys are responsible for proper management and protection
#            of private keys.  Vendors may choose to use infrastructure such as signing servers
#            or signing portals to support the management and protection of private keys.
#
##################
*_*_*_RSA2048SHA256SIGN_PATH = Rsa2048Sha256Sign
*_*_*_RSA2048SHA256SIGN_GUID = A7717414-C616-4977-9420-844712A735BF

##################
# BrotliCompress tool definitions
##################
*_*_*_BROTLI_PATH        = BrotliCompress
*_*_*_BROTLI_GUID        = 3D532050-5CDA-4FD0-879E-0F7F630D5AFB

##################
# LzmaCompress tool definitions
##################
*_*_*_LZMA_PATH          = LzmaCompress
*_*_*_LZMA_GUID          = EE4E5898-3914-4259-9D6E-DC7BD79403CF

##################
# LzmaF86Compress tool definitions with converter for x86 code.
# It can improve the compression ratio if the input file is IA32 or X64 PE image.
##################
*_*_*_LZMAF86_PATH         = LzmaF86Compress
*_*_*_LZMAF86_GUID         = D42AE6BD-1352-4bfb-909A-CA72A6EAE889

##################
# TianoCompress tool definitions
##################
*_*_*_TIANO_PATH         = TianoCompress
*_*_*_TIANO_GUID         = A31280AD-481E-41B6-95E8-127F4C984779

##################
# BPDG tool definitions
##################
*_*_*_VPDTOOL_PATH         = BPDG
*_*_*_VPDTOOL_GUID         = 8C3D856A-9BE6-468E-850A-24F7A8D38E08

##################
# Pkcs7Sign tool definitions
##################
*_*_*_PKCS7SIGN_PATH       = Pkcs7Sign
*_*_*_PKCS7SIGN_GUID       = 4AAFD29D-68DF-49EE-8AA9-347D375665A7

##################
# NASM tool definitions
##################
*_*_*_NASM_PATH                = ENV(NASM_PREFIX)nasm
# NASMB uses NASM produce a .bin from a .nasmb NASM source file
*_*_*_NASMB_FLAGS              = -f bin

#################
# Build rule order
#################
*_*_*_*_BUILDRULEORDER = nasm asm Asm ASM S s nasmb asm16
