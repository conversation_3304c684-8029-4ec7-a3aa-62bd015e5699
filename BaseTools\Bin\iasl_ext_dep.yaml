## @file
# Download iasl executable tool from a nuget.org package
# - package contains different binaries based on host
# Add the folder with the tool to the path
#
# This is only downloaded for scope cibuild thus
# should have no impact on the asl compiler used by any
# given platform to build.
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
  "id": "iasl-ci-1",
  "scope": "cibuild",
  "type": "nuget",
  "name": "edk2-acpica-iasl",
  "source": "https://pkgs.dev.azure.com/projectmu/acpica/_packaging/mu_iasl/nuget/v3/index.json",
  "version": "20200717.0.0",
  "flags": ["set_path", "host_specific"]
}
