#/** @file
#  Implement ArmGenericTimerCounterLib using the physical timer
#
#  Copyright (c) 2014, Linaro Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGenericTimerPhyCounterLib
  FILE_GUID                      = 7A07E61D-9967-407F-AE85-2EB0B50BEF2C
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmGenericTimerCounterLib

[Sources]
  ArmGenericTimerPhyCounterLib.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
