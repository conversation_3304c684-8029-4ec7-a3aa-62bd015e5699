## @file
# Instance of CpuExceptionHandlerLib Library for ARM/AArch64 architectures
#
# This library instance is used for modules that will implement exception
# handlers in-place (by programming VBAR).  The exception handlers will be
# generated with alignment as required by the processor architecture.  The
# alignment must be propagated into the parent FFS/FV through FDF build rules
# for the relevant module types (i.e. Align=Auto).
#
#  Copyright (c) 2011-2012, ARM Limited. All rights reserved.
#  Copyright (c) 2016 HP Development Company, L.P.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmExceptionLib
  FILE_GUID                      = ********-4E88-47F0-87C5-D96A1D270539
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CpuExceptionHandlerLib

[Sources.common]
  ArmExceptionLib.c

[Sources.Arm]
  Arm/ArmException.c
  Arm/ExceptionSupport.S   | GCC

[Sources.AARCH64]
  AArch64/AArch64Exception.c
  AArch64/ExceptionSupport.S

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  BaseMemoryLib
  CacheMaintenanceLib
  DebugLib
  DefaultExceptionHandlerLib
  MemoryAllocationLib

[Pcd]
