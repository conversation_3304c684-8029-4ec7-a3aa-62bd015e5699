#/** @file
#
#  Copyright (c) 2011-2015, ARM Limited. All rights reserved.
#  Copyright (c) 2014-2020, Linaro Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformPeiLib
  FILE_GUID                      = 59C11815-F8DA-4F49-B4FB-EC1E41ED1F06
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformPeiLib

[Sources]
  PlatformPeiLib.c

[Packages]
  ArmPkg/ArmPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  OvmfPkg/OvmfPkg.dec
  SecurityPkg/SecurityPkg.dec

[FeaturePcd]
  gArmVirtTokenSpaceGuid.PcdTpm2SupportEnabled

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  HobLib
  FdtLib
  FdtSerialPortAddressLib
  PcdLib
  PeiServicesLib

[FixedPcd]
  gArmTokenSpaceGuid.PcdFvSize
  gUefiOvmfPkgTokenSpaceGuid.PcdDeviceTreeAllocationPadding

[Pcd]
  gArmTokenSpaceGuid.PcdFvBaseAddress
  gEfiSecurityPkgTokenSpaceGuid.PcdTpmBaseAddress             ## SOMETIMES_PRODUCES
  gUefiOvmfPkgTokenSpaceGuid.PcdDeviceTreeInitialBaseAddress

[Ppis]
  gOvmfTpmDiscoveredPpiGuid                                   ## SOMETIMES_PRODUCES
  gPeiTpmInitializationDonePpiGuid                            ## SOMETIMES_PRODUCES

[Guids]
  gEarlyPL011BaseAddressGuid
  gFdtHobGuid

[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid
