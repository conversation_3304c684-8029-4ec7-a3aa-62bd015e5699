## @file
#  ARM MP services protocol driver
#
#  Copyright (c) 2022, Qualcomm Innovation Center, Inc. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 1.27
  BASE_NAME                      = ArmPsciMpServicesDxe
  FILE_GUID                      = 007ab472-dc4a-4df8-a5c2-abb4a327278c
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = ArmPsciMpServicesDxeInitialize

[Sources.Common]
  ArmPsciMpServicesDxe.c
  MpFuncs.S
  MpServicesInternal.h

[Packages]
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  ArmLib
  ArmMmuLib
  ArmSmcLib
  BaseMemoryLib
  CacheMaintenanceLib
  CpuExceptionHandlerLib
  DebugLib
  HobLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[Protocols]
  gEfiMpServiceProtocolGuid            ## PRODUCES
  gEfiLoadedImageProtocolGuid          ## CONSUMES

[Guids]
  gArmMpCoreInfoGuid

[Depex]
  TRUE

[BuildOptions]
  GCC:*_*_*_CC_FLAGS = -mstrict-align
