## @file
# Azure Pipeline build file for a build using Ubuntu and GCC
#
# Copyright (c) Microsoft Corporation.
# Copyright (c) 2020, Hewlett Packard Enterprise Development LP. All rights reserved.<BR>
# Copyright (c) 2022, Loongson Technology Corporation Limited. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
trigger:
- master
- stable/*
pr:
- master
- stable/*

variables:
  - template: templates/defaults.yml

jobs:
- template: templates/pr-gate-build-job.yml
  parameters:
    tool_chain_tag: 'GCC5'
    vm_image: 'ubuntu-24.04'
    container: ${{ variables.default_linux_image }}
    arch_list: "IA32,X64,ARM,AARCH64,RISCV64,LOONGARCH64"
    usePythonVersion: ''  # use Python from the container image
