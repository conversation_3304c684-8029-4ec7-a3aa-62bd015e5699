#/** @file
#
#  Copyright (c) 2017, Linaro, Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010018
  BASE_NAME                      = ArmCrashDumpDxe
  FILE_GUID                      = 0bda00b0-05d6-4bb8-bfc7-058ad13615cf
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = ArmCrashDumpDxeInitialize

[Sources]
  ArmCrashDumpDxe.c

[Packages]
  ArmPkg/ArmPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  DebugLib
  DefaultExceptionHandlerLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint

[Protocols]
  gEfiCpuArchProtocolGuid

[Depex]
  gEfiCpuArchProtocolGuid
