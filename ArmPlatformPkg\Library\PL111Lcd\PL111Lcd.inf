#/** @file PL111Lcd.inf
#
#  Component description file for PL111Lcd module
#
#  Copyright (c) 2011-2017, ARM Ltd. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PL111Lcd
  FILE_GUID                      = 407B4008-BF5B-11DF-9547-CF16E0D72085
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = LcdHwLib

[Sources.common]
  PL111Lcd.h
  PL111Lcd.c

[Packages]
  ArmPlatformPkg/ArmPlatformPkg.dec
  ArmPkg/ArmPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiLib
  BaseLib
  DebugLib
  IoLib

[FixedPcd]
  gArmPlatformTokenSpaceGuid.PcdPL111LcdBase
