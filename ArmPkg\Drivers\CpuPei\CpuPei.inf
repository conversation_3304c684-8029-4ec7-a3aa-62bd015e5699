## @file
# Component description file for BootMode module
#
# This module provides platform specific function to detect boot mode.
# Copyright (c) 2006 - 2010, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2023, Google, LLC. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuPei
  FILE_GUID                      = 2FD8B7AD-F8FA-4021-9FC0-0AA572147CDC
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InitializeCpuPeim

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM
#

[Sources]
  CpuPei.c

[Packages]
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  ArmLib
  ArmMmuLib

[Ppis]
  gArmMpCoreInfoPpiGuid
  gEdkiiMemoryAttributePpiGuid

[Guids]
  gArmMpCoreInfoGuid

[FixedPcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiCpuIoSize

[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid

