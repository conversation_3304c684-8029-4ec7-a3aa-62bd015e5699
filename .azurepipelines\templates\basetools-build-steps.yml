## @file
# File templates/basetools-build-job.yml
#
# template file to build basetools
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
parameters:
  tool_chain_tag: ''

steps:
- task: CmdLine@1
  displayName: Build Base Tools from source
  inputs:
    filename: python
    arguments: BaseTools/Edk2ToolsBuild.py -t ${{ parameters.tool_chain_tag }}
  condition: and(gt(variables.pkg_count, 0), succeeded())

- task: CopyFiles@2
  displayName: "Copy base tools build log"
  inputs:
    targetFolder: '$(Build.ArtifactStagingDirectory)'
    SourceFolder: 'BaseTools/BaseToolsBuild'
    contents: |
      BASETOOLS_BUILD*.*
    flattenFolders: true
  condition: and(gt(variables.pkg_count, 0), succeededOrFailed())
