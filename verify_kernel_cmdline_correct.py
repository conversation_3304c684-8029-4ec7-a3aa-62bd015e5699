#!/usr/bin/env python3
"""
验证TDX Event Log中EV_IPL事件的SHA384 digest
重新分析内核命令行参数的度量
"""

import hashlib
import binascii

def verify_kernel_cmdline_digest():
    """验证内核命令行的SHA384 digest"""
    
    # 从事件日志中提取的期望digest
    expected_digest = "2a7a405c2abfcc816b310a54689f02f0901bef5fd9b7ab1c85fa267165d820772c5a101fe425b354c6169bc50afca043"
    
    # Event Data: 从原始RAW DATA中逐字节提取的120字节
    # 从0x7FBF3697开始的120字节
    event_data_hex = (
        "6B65726E656C5F636D646C696E653A202F766D6C696E757A2D362E31312E" +
        "302D32362D67656E6572696320726F6F743D555549443D663637313232653" +
        "92D626561612D343936392D626432342D366362393237383234643239207" +
        "26F20636F6E736F6C653D747479312063636F6E736F6C653D747479533000"
    )
    
    print("=== TDX Event Log EV_IPL (内核命令行) Digest 验证 ===\n")
    
    try:
        # 转换为字节数组
        event_data = binascii.unhexlify(event_data_hex)
        print(f"事件数据长度: {len(event_data)} bytes")
        print(f"期望长度: 120 bytes")
        print(f"长度匹配: {'✓' if len(event_data) == 120 else '✗'}\n")
        
        # 解析内容
        cmdline_str = event_data.decode('utf-8', errors='replace').rstrip('\x00')
        print(f"内核命令行内容:")
        print(f"  {cmdline_str}")
        print()
        
        # 计算SHA384
        sha384_hash = hashlib.sha384(event_data).hexdigest()
        
        print("Digest 计算结果:")
        print(f"  计算得到: {sha384_hash}")
        print(f"  事件日志: {expected_digest}")
        print(f"  匹配结果: {'✓ 验证成功' if sha384_hash == expected_digest else '✗ 验证失败'}")
        
        return sha384_hash == expected_digest
        
    except Exception as e:
        print(f"错误: {e}")
        return False

def analyze_kernel_parameters():
    """分析内核参数"""
    print("\n=== 内核参数详细分析 ===")
    
    # 完整的命令行（去掉前缀）
    cmdline = "/vmlinuz-6.11.0-26-generic root=UUID=f67122e9-beaa-4969-bd24-6cb927824d29 ro console=tty1 console=ttyS0"
    
    print(f"完整命令行: {cmdline}")
    print()
    
    # 解析各个部分
    parts = cmdline.split()
    kernel_image = parts[0]
    parameters = parts[1:]
    
    print(f"内核镜像: {kernel_image}")
    print("启动参数:")
    
    for param in parameters:
        if '=' in param:
            key, value = param.split('=', 1)
            print(f"  {key} = {value}")
        else:
            print(f"  {param} (标志)")
    
    print("\n参数说明:")
    print("  root=UUID=... : 根文件系统的UUID")
    print("  ro            : 以只读方式挂载根文件系统")
    print("  console=tty1  : 输出到第一个虚拟终端")
    print("  console=ttyS0 : 输出到第一个串行端口")

def create_verification_command():
    """生成验证命令"""
    print("\n=== 简单验证命令 ===")
    
    # 提取120字节的事件数据
    event_data_hex = "6B65726E656C5F636D646C696E653A202F766D6C696E757A2D362E31312E302D32362D67656E6572696320726F6F743D555549443D663637313232653992D626561612D343936392D626432342D366362393237383234643239207226F20636F6E736F6C653D747479312063636F6E736F6C653D747479533000"
    
    print("使用openssl验证:")
    print(f'echo -n "{event_data_hex}" | xxd -r -p | openssl dgst -sha384')
    
    print("\n使用Python验证:")
    print(f"""python3 -c "
import hashlib, binascii
data = '{event_data_hex}'
result = hashlib.sha384(binascii.unhexlify(data)).hexdigest()
expected = '2a7a405c2abfcc816b310a54689f02f0901bef5fd9b7ab1c85fa267165d820772c5a101fe425b354c6169bc50afca043'
print(f'计算: {{result}}')
print(f'期望: {{expected}}')
print(f'验证: {{\"✓\" if result == expected else \"✗\"}}')
" """)

def analyze_security_implications():
    """分析安全含义"""
    print("\n=== 安全含义分析 ===")
    
    print("EV_IPL事件的作用:")
    print("  ✓ 度量内核命令行参数")
    print("  ✓ 确保启动参数的完整性")
    print("  ✓ 防止恶意修改内核参数")
    print("  ✓ 建立可信的操作系统加载过程")
    
    print("\n关键安全参数:")
    print("  • root=UUID=... : 确保从正确的根文件系统启动")
    print("  • ro            : 防止意外修改根文件系统")
    print("  • console=...   : 控制系统输出，防止信息泄露")
    
    print("\nTDX环境意义:")
    print("  • 在可信执行环境中验证启动参数")
    print("  • 为远程证明提供启动配置证据")
    print("  • 确保虚拟机启动的可信性")

def main():
    """主函数"""
    success = verify_kernel_cmdline_digest()
    analyze_kernel_parameters()
    create_verification_command()
    analyze_security_implications()
    
    print(f"\n=== 验证结果 ===")
    print(f"Digest验证: {'通过' if success else '失败'}")

if __name__ == "__main__":
    main()
