# TianoCore edk2 GitHub Bug Report Template
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
#

name: 🐛 Bug Report
description: File a bug report
title: "[Bug]: <title>"
labels: ["type:bug", "state:needs-triage"]

body:
  - type: markdown
    attributes:
      value: |
        👋 Thanks for taking the time to fill out this bug report!

  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: |
        Please search to see if an issue already exists for the bug you encountered.
        &nbsp;&nbsp;[Seach existing issues](https://github.com/tianocore/edk2/issues)
      options:
      - label: I have searched existing issues
        required: true

  - type: checkboxes
    id: bug_type
    attributes:
      label: Bug Type
      description: |
        What type of code does this bug affect?
      options:
        - label: Firmware
        - label: Tool
        - label: Unit Test

  - type: dropdown
    id: packages_impacted
    attributes:
      label:  What packages are impacted?
      description: |
        *Select all that apply*
      multiple: true
      options:
        - ArmPkg
        - ArmPlatformPkg
        - ArmVirtPkg
        - BaseTools
        - Build or CI Code
        - CryptoPkg
        - DynamicTablesPkg
        - EmbeddedPkg
        - EmulatorPkg
        - FatPkg
        - FmpDevicePkg
        - IntelFsp2Pkg
        - IntelFsp2WrapperPkg
        - MdeModulePkg
        - MdePkg
        - NetworkPkg
        - OvmfPkg
        - PcAtChipsetPkg
        - PrmPkg
        - RedfishPkg
        - SecurityPkg
        - ShellPkg
        - SignedCapsulePkg
        - SourceLevelDebugPkg
        - StandaloneMmPkg
        - UefiCpuPkg
        - UefiPayloadPkg
        - UnitTestFrameworkPkg
        - Other
    validations:
      required: true

  - type: dropdown
    id: targets_impacted
    attributes:
      label: Which targets are impacted by this bug?
      description: |
        *Select all that apply*
      multiple: true
      options:
        - DEBUG
        - NO-TARGET
        - NOOPT
        - RELEASE

  - type: textarea
    id: current_behavior
    attributes:
      label: Current Behavior
      description: A concise description of the bug that you're experiencing.
    validations:
      required: true

  - type: textarea
    id: expected_behavior
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true

  - type: textarea
    id: steps_to_reproduce
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        <example>
        1. In this environment (OS, toolchain, platform info, etc.)...
        2. Acquire the source code using these commands...
        3. Build the code using these commands...
        4. Flash the image using these commands...
        5. Boot using this process...
        6. Change option(s)...
        7. See error...
    validations:
      required: true

  - type: textarea
    id: build_environment
    attributes:
      label: Build Environment
      description: |
        Examples:
          - **OS**: Ubuntu 24.04 or Windows 11...
          - **Tool Chain**: GCC5 or VS2022 or CLANGPDB...
      value: |
          - OS(s):
          - Tool Chain(s):
      render: markdown
    validations:
      required: true

  - type: textarea
    id: version_info
    attributes:
      label: Version Information
      description: >
        What version of this repo is known to reproduce the problem?


        The problem is assumed to be present from this version and later. If an earlier version is not known other than
        the latest commit, indicate that and put the current *edk2/master* commit SHA.
      placeholder: |
        Commit: <SHA>
        -or-
        Tag: <Tag>
      render: text
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        **Urgency Key**
        - 🟢 **Low**
          - A minor change with little to no important functional impact
          - It is not important to fix this in a specific time frame
        - 🟡 **Medium**
          - An important change with a functional impact
          - Will be prioritized above *low* issues in the normal course of development
        - 🔥 **High**
          - A critical change that has a significant functional impact
          - Must be fixed immediately

  - type: dropdown
    id: urgency
    attributes:
      label: Urgency
      description: How urgent is it to fix this bug?
      multiple: false
      options:
        - Low
        - Medium
        - High
    validations:
      required: true

  - type: dropdown
    id: fix_owner
    attributes:
      label: Are you going to fix this?
      description: Indicate if you are going to fix this or requesting someone else fix it.
      multiple: false
      options:
        - I will fix it
        - Someone else needs to fix it
    validations:
      required: true

  - type: dropdown
    id: needs_maintainer_feedback
    attributes:
      label: Do you need maintainer feedback?
      description: Indicate if you would like a maintainer to provide feedback on this submission.
      multiple: false
      options:
        - No maintainer feedback needed
        - Maintainer feedback requested
    validations:
      required: true

  - type: textarea
    id: anything_else
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering.

        Serial debug logs and/or debugger logs are especially helpful!

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
