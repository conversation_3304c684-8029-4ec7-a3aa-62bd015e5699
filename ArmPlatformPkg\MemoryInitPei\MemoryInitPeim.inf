#/** @file
#
#  Copyright (c) 2018, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2011-2014, ARM Ltd. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = MemoryInit
  FILE_GUID                      = c61ef796-b50d-4f98-9f78-4f6f79d800d5
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InitializeMemory

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC ARM
#

[Sources]
  MemoryInitPeim.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  ArmLib
  ArmPlatformLib
  MemoryInitPeiLib

[Guids]
  gEfiMemoryTypeInformationGuid

[FeaturePcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiProduceMemoryTypeInformationHob

[FixedPcd]
  gArmTokenSpaceGuid.PcdFdBaseAddress
  gArmTokenSpaceGuid.PcdFdSize

  gArmPlatformTokenSpaceGuid.PcdSystemMemoryUefiRegionSize

  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode

[Pcd]
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmTokenSpaceGuid.PcdSystemMemorySize

[Depex]
  TRUE
