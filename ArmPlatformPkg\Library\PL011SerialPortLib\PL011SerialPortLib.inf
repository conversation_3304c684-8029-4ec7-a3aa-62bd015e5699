#/** @file
#
#  Component description file for PL011SerialPortLib module
#
#  Copyright (c) 2011-2016, ARM Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PL011SerialPortLib
  FILE_GUID                      = 8ecefc8f-a2c4-4091-b80f-20f7aeb0567f
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = SerialPortLib

[Sources.common]
  PL011SerialPortLib.c

[LibraryClasses]
  PL011UartClockLib
  PL011UartLib
  PcdLib

[Packages]
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialRegisterBase

[FixedPcd]
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultBaudRate
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultDataBits
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultParity
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultStopBits
  gArmPlatformTokenSpaceGuid.PL011UartClkInHz
