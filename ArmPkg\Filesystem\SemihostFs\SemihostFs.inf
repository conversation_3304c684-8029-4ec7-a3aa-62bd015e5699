#/** @file
#  Support a Semi Host file system over a debuggers JTAG
#
#  Copyright (c) 2009, Apple Inc. All rights reserved.<BR>
#  Portions copyright (c) 2011 - 2013, ARM Ltd. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SemihostFs
  FILE_GUID                      = C5B9C74A-6D72-4719-99AB-C59F199091EB
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = SemihostFsEntryPoint

[Sources.ARM, Sources.AARCH64]
  Arm/SemihostFs.c

[Packages]
  MdePkg/MdePkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  BaseLib
  MemoryAllocationLib
  SemihostLib
  UefiDriverEntryPoint
  UefiLib

[Guids]
  gEfiFileSystemInfoGuid
  gEfiFileInfoGuid
  gEfiFileSystemVolumeLabelInfoIdGuid

[Protocols]
  gEfiSimpleFileSystemProtocolGuid
  gEfiDevicePathProtocolGuid

