## @file
# Azure Pipeline build file for a build using Windows and Visual Studio
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
trigger:
- master
- stable/*

pr:
- master
- stable/*

variables:
  - template: templates/defaults.yml

jobs:
- template: templates/pr-gate-build-job.yml
  parameters:
    tool_chain_tag: 'VS2022'
    vm_image: 'windows-2022'
    arch_list: "IA32,X64"
    usePythonVersion: ${{ variables.default_python_version }}
    extra_install_step:
    - powershell: choco install opencppcoverage; Write-Host "##vso[task.prependpath]C:\Program Files\OpenCppCoverage"
      displayName: Install Code Coverage Tool
      condition: and(gt(variables.pkg_count, 0), succeeded())

