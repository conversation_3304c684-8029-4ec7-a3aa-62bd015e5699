#/** @file
#
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2012 - 2017, ARM Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmGicDxe
  FILE_GUID                      = DE371F7C-DEC4-4D21-ADF1-593ABCC15882
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0

  ENTRY_POINT                    = InterruptDxeInitialize

[Sources.common]
  ArmGicDxe.h
  ArmGicDxe.c
  ArmGicCommonDxe.c

  GicV2/ArmGicV2Dxe.c
  GicV3/ArmGicV3Dxe.c

[Sources.ARM]
  GicV3/Arm/ArmGicV3.S     | GCC

[Sources.AARCH64]
  GicV3/AArch64/ArmGicV3.S

[Packages]
  MdePkg/MdePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
  UefiLib
  UefiBootServicesTableLib
  DebugLib
  PrintLib
  MemoryAllocationLib
  UefiDriverEntryPoint
  IoLib
  PcdLib
  UefiLib

[Protocols]
  gHardwareInterruptProtocolGuid  ## PRODUCES
  gHardwareInterrupt2ProtocolGuid ## PRODUCES
  gEfiCpuArchProtocolGuid         ## CONSUMES

[Pcd.common]
  gArmTokenSpaceGuid.PcdGicDistributorBase
  gArmTokenSpaceGuid.PcdGicRedistributorsBase
  gArmTokenSpaceGuid.PcdGicInterruptInterfaceBase

[Depex]
  gEfiCpuArchProtocolGuid
