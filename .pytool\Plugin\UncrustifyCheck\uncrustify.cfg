## @file
# Uncrustify Configuration File for EDK II C Code
#
# Coding Standard: https://edk2-docs.gitbook.io/edk-ii-c-coding-standards-specification/
#
# This configuration file is meant to be a "best attempt" to align with the
# definitions in the EDK II C Coding Standards Specification.
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

# Force UTF-8 encoding (no UTF-16)
enable_digraphs                 = false
utf8_byte                       = false
utf8_force                      = true

# Code width / line splitting
#code_width                      =120     # TODO: This causes non-deterministic behaviour in some cases when code wraps
ls_code_width                   =false
ls_for_split_full               =true
ls_func_split_full              =true
pos_comma                       =trail

# 5.1.7  All files must end with CRLF
newlines                        = crlf

# 5.1.2 Do not use tab characters

cmt_convert_tab_to_spaces       = true      # Whether to convert all tabs to spaces in comments. If false, tabs in
                                            # comments are left alone, unless used for indenting.
indent_columns                  = 2         # Number of spaces for indentation
indent_with_tabs                = 0         # Do not use TAB characters
string_replace_tab_chars        = true      # Replace TAB with SPACE
                                            # Note: This will break .robot files but is needed for edk2 style

# 5.2.1.1 There shall be only one statement on a line (statement ends with ;)
nl_multi_line_cond              = true      # Add a newline between ')' and '{' if the ')' is on a different line than
                                            # the if/for/etc.
nl_after_semicolon              = true      # Whether to add a newline after semicolons, except in 'for' statements.

# 5.2.1.3 An open brace '{' goes on the same line as the closing parenthesis ')' of simple predicate expressions
mod_full_brace_do               = add       # Add or remove braces on a single-line 'do' statement.
mod_full_brace_for              = add
mod_full_brace_function         = add       # Add or remove braces on a single-line function definition.
mod_full_brace_if               = add       # Add or remove braces on a single-line 'if' statement. Braces will not be
                                            # removed if the braced statement contains an 'else'.
mod_full_brace_if_chain         = false
mod_full_brace_while            = add

# 5.2.1.4 A close brace '}' always goes at the beginning of the last line of the body
eat_blanks_after_open_brace     = true
eat_blanks_before_close_brace   = true      # Whether to remove blank lines before '}'.

# 5.2.2.2 Always put space before and after binary operators.
sp_assign                       = add       # Add or remove space around assignment operator '=', '+=', etc.
sp_assign_default               = add
sp_bool                         = add       # Add or remove space around boolean operators '&&' and '||'.
sp_compare                      = add       # Add or remove space around compare operator '<', '>', '==', etc.

# ******* Do not put space between unary operators and their object
sp_addr                         = remove    # A or remove space after the '&' (address-of) unary operator.
sp_incdec                       = remove    # Add or remove space between '++' and '--' the word to which it is being
                                            # applied, as in '(--x)' or 'y++;'.
sp_inv                          = remove    # Add or remove space after the '~' (invert) unary operator.
sp_not                          = remove    # Add or remove space after the '!' (not) unary operator.
sp_sign                         = remove    # Add or remove space after '+' or '-', as in 'x = -5' or 'y = +7'.

# ******* Subsequent lines of multi-line function calls should line up two spaces from the beginning of the function
#         name
nl_func_call_args_multi_line    = true      # Whether to add a newline after each ',' in a function call if '(' and ')'
                                            # are in different lines.
nl_func_call_args_multi_line_ignore_closures = false

# - Indent each argument 2 spaces from the start of the function name. If a
#   function is called through a structure or union member, of type
#   pointer-to-function, then indent each argument 2 spaces from the start of the
#   member name.
indent_func_call_edk2_style     = true      # Use EDK2 indentation style for function calls  (**CUSTOM SETTING**)
indent_paren_after_func_call    = true      # Whether to indent the open parenthesis of a function call, if the
                                            # parenthesis is on its own line.

# - Align the close parenthesis with the start of the last argument
indent_paren_close              = 0         # How to indent a close parenthesis after a newline.
                                            # (0: Body, 1: Openparenthesis, 2: Brace level)


# 5.2.2.5 Always put space after commas or semicolons that separate items
sp_after_comma                  = force     # Add or remove space after ',', i.e. 'a,b' vs. 'a, b'.
sp_before_comma                 = remove    # Add or remove space before ','.

# 5.2.2.6 Always put space before an open parenthesis
sp_after_sparen                 = add       # Add or remove space after ')' of control statements.
sp_attribute_paren              = add       # Add or remove space between '__attribute__' and '('.
sp_before_sparen                = force     # Add or remove space before '(' of control statements
                                            # ('if', 'for', 'switch', 'while', etc.).
sp_defined_paren                = force     # Add or remove space between 'defined' and '(' in '#if defined (FOO)'.
sp_func_call_paren              = force     # Add or remove space between function name and '(' on function calls.
sp_func_call_paren_empty        = force     # Add or remove space between function name and '()' on function calls
                                            # without parameters. If set to ignore (the default), sp_func_call_paren is
                                            # used.
sp_func_def_paren               = add       # Add or remove space between alias name and '(' of a non-pointer function
                                            # type typedef.
sp_func_proto_paren             = add       # Add or remove space between function name and '()' on function declaration
sp_sizeof_paren                 = force     # Add or remove space between 'sizeof' and '('.
sp_type_func                    = add       # Add or remove space between return type and function name. A minimum of 1
                                            # is forced except for pointer return types.

# Not specified, but also good style to remove spaces inside parentheses (Optional)
sp_cparen_oparen                = remove    # Add or remove space between back-to-back parentheses, i.e. ')(' vs. ') ('.
sp_inside_fparen                = remove    # Add or remove space inside function '(' and ')'.
sp_inside_fparens               = remove    # Add or remove space inside empty function '()'.
sp_inside_paren                 = remove    # Add or remove space inside '(' and ')'.
sp_inside_paren_cast            = remove    # Add or remove spaces inside cast parentheses. '(int)x'
sp_inside_square                = remove    # Add or remove space inside a non-empty '[' and ']'.
sp_paren_paren                  = remove    # Add or remove space between nested parentheses, i.e. '((' vs. ') )'.
sp_square_fparen                = remove    # Add or remove space between ']' and '(' when part of a function call.

# 5.2.2.7 Put a space before an open brace if it is not on its own line
sp_do_brace_open                = force     # Add or remove space between 'do' and '{'.
sp_paren_brace                  = force     # Add or remove space between ')' and '{'.
sp_sparen_brace                 = force     # Add or remove space between ')' and '{' of of control statements.

# 5.2.2.8 Do not put spaces around structure member and pointer operators
sp_after_byref                  = remove    # Add or remove space after reference sign '&', if followed by a word.
sp_before_byref                 = add       # Add or remove space before a reference sign '&'.
sp_deref                        = remove    # Add or remove space after the '*' (dereference) unary operator. This does
                                            # not affect the spacing after a '*' that is part of a type.
sp_member                       = remove    # Add or remove space around the '.' or '->' operators.

# ******* Do not put spaces before open brackets of array subscripts
sp_before_square                = remove    # Add or remove space before '[' (except '[]').
sp_before_squares               = remove    # Add or remove space before '[]'.
sp_before_vardef_square         = remove    # Add or remove space before '[' for a variable definition.

# ******** Use extra parentheses rather than depending on in-depth knowledge of the order of precedence of C
mod_full_paren_if_bool          = true      # Whether to fully parenthesize Boolean expressions in 'while' and 'if'
                                            # statement, as in 'if (a && b > c)' => 'if (a && (b > c))'.

# ******** Align a continuation line with the part of the line that it continues.
use_indent_continue_only_once   = true

# Additional '{}' bracing rules (Optional)
# NOTE - The style guide specifies two different styles for braces,
# so these are ignored for now to allow developers some flexibility.
nl_after_brace_close            = true      # Whether to add a newline after '}'. Does not apply if followed by a
                                            # necessary ';'.
nl_brace_else                   = remove    # Add or remove newline between '}' and 'else'.
nl_brace_while                  = remove    # Add or remove newline between '}' and 'while' of 'do' statement.
nl_do_brace                     = remove    # Add or remove newline between 'do' and '{'.
nl_else_brace                   = remove    # Add or remove newline between 'else' and '{'.
nl_else_if                      = remove    # Add or remove newline between 'else' and 'if'.
nl_elseif_brace                 = remove    # Add or remove newline between 'else if' and '{'.
nl_enum_brace                   = remove    # Add or remove newline between 'enum' and '{'.
nl_fcall_brace                  = remove    # Add or remove newline between a function call's ')' and '{',
                                            # as in 'list_for_each(item, &list) { }'.
nl_for_brace                    = remove    # Add or remove newline between 'for' and '{'.
nl_if_brace                     = remove    # Add or remove newline between 'if' and '{'.
nl_struct_brace                 = remove    # Add or remove newline between 'struct and '{'.
nl_switch_brace                 = remove    # Add or remove newline between 'switch' and '{'.
nl_union_brace                  = remove    # Add or remove newline between 'union' and '{'.
nl_while_brace                  = remove    # Add or remove newline between 'while' and '{'.

# Additional whitespace rules (Optional)
sp_after_ptr_star               = remove    # Add or remove space after pointer star '*', if followed by a word.
                                            # Useful when paired with align_var_def_star_style==2
sp_after_ptr_star_func          = remove    # Add or remove space after a pointer star '*', if followed by a function
                                            # prototype or function definition.
sp_after_semi                   = remove    # Add or remove space after ';', except when followed by a comment.
sp_before_case_colon            = remove    # Add or remove space before case ':'.
sp_before_ptr_star              = add       # Add or remove space before pointer star '*'.
sp_before_ptr_star_func         = add       # Add or remove space before a pointer star '*', if followed by a function
                                            # prototype or function definition.
sp_before_semi                  = remove    # Add or remove space before ';'
sp_before_semi_for              = remove    # Add or remove space before ';' in non-empty 'for' statements.
sp_before_semi_for_empty        = add       # Add or remove space before a semicolon of an empty part of a for statement
sp_between_ptr_star             = remove    # Add or remove space between pointer stars '*'. (ie, 'VOID **')
sp_brace_close_while            = force     # Add or remove space between '}' and 'while'.

sp_after_cast                   = remove
sp_after_type                   = add
sp_balance_nested_parens        = false
sp_before_nl_cont               = add
sp_before_square_asm_block      = ignore
sp_before_unnamed_byref         = add
sp_brace_brace                  = ignore
sp_brace_else                   = force
sp_brace_typedef                = add
sp_case_label                   = force
sp_cmt_cpp_doxygen              = true
sp_cond_colon                   = add
sp_cond_question                = add
sp_cpp_cast_paren               = force
sp_else_brace                   = force
sp_endif_cmt                    = force
sp_enum_assign                  = add
sp_inside_braces                = force
sp_inside_braces_empty          = force
sp_inside_braces_enum           = force
sp_inside_braces_struct         = force
sp_pp_concat                    = add
sp_pp_stringify                 = add
sp_return_paren                 = add
sp_special_semi                 = force
sp_while_paren_open             = force

# Additional Indentation Rules
indent_access_spec              = 1
indent_access_spec_body         = false
indent_align_assign             = true
indent_align_string             = true
indent_bool_paren               = true
indent_brace_parent             = false
indent_braces                   = false
indent_braces_no_class          = false
indent_braces_no_func           = true
indent_braces_no_struct         = false
indent_class                    = true
indent_class_colon              = false
indent_cmt_with_tabs            = false         # Whether to indent comments that are not at a brace level with tabs on
                                                # a tabstop. Requires indent_with_tabs=2. If false, will use spaces.
indent_col1_comment             = true
indent_col1_multi_string_literal= true
indent_comma_paren              = true
indent_else_if                  = true
indent_extern                   = true
indent_first_bool_expr          = true

indent_func_def_param_paren_pos_threshold = 0
indent_func_param_double                  = false
indent_func_proto_param                   = true
indent_ignore_asm_block                   = true
indent_label                              = 1
indent_member                             = 2
indent_namespace                          = false
indent_param                              = 2
indent_paren_nl                           = false
indent_paren_open_brace                   = false
indent_preserve_sql                       = false
indent_relative_single_line_comments      = false
indent_sing_line_comments                 = 0
indent_single_newlines                    = false
indent_square_nl                          = false
indent_switch_case                        = 2
indent_template_param                     = true
indent_var_def_blk                        = 0
indent_var_def_cont                       = false

# Tidy-up rules (Optional)
mod_move_case_break             = true      # Whether to move a 'break' that appears after a fully braced 'case'
                                            # before the close brace, as in 'case X: { ... } break;' =>
                                            # 'case X: { ... break; }'.
mod_pawn_semicolon              = false
mod_remove_empty_return         = false     # Whether to remove a void 'return;' that appears as the last statement
                                            # in a function.
mod_remove_extra_semicolon      = true
mod_sort_import                 = false
mod_sort_include                = false
mod_sort_using                  = false
nl_after_case                   = false     # Whether to add a newline after a 'case' statement.
nl_end_of_file                  = force     # Add or remove newline at the end of the file.
nl_end_of_file_min              = 1         # The minimum number of newlines at the end of the file
nl_max                          = 2         # The maximum number of consecutive newlines (3 = 2 blank lines).
nl_start_of_file                = remove    # Add or remove newlines at the start of the file.

# Code alignment rules (Optional)
align_asm_colon                 = false
align_assign_span               = 1         # The span for aligning on '=' in assignments.
align_assign_thresh             = 0
align_edk2_style                = true      # Whether to apply edk2-specific alignment formatting
align_enum_equ_span             = 1         # The span for aligning on '=' in enums.
align_func_params               = true      # Whether to align variable definitions in prototypes and functions.
align_func_params_gap           = 2
align_func_params_span          = 2         # The span for aligning parameter definitions in function on parameter name.
align_func_params_thresh        = 0
align_func_proto_span           = 0
align_keep_tabs                 = false
align_left_shift                = false
align_mix_var_proto             = false
align_nl_cont                   = false
align_oc_decl_colon             = false
align_on_operator               = false
align_on_tabstop                = false
align_pp_define_gap             = 2
align_pp_define_span            = 1
align_right_cmt_at_col          = 0         # Align trailing comment at or beyond column N; 'pulls in' comments as
                                            # a bonus side effect (0=ignore)
align_right_cmt_gap             = 0         # If a trailing comment is more than this number of columns away from the
                                            # text it follows,
                                            # it will qualify for being aligned. This has to be > 0 to do anything.
align_right_cmt_mix             = false     # If aligning comments, mix with comments after '}' and #endif with less
                                            # than 3 spaces before the comment
align_right_cmt_same_level      = true      # Whether to only align trailing comments that are at the same brace level.
align_right_cmt_span            = 2         # The span for aligning comments that end lines.
align_same_func_call_params     = false
align_single_line_brace         = true
align_single_line_func          = true
align_struct_init_span          = 1         # The span for aligning struct initializer values.
align_typedef_amp_style         = 1
align_typedef_func              = 1         # How to align typedef'd functions with other typedefs.
                                            # (0: No align, 1: Align open paranthesis, 2: Align function type name)
align_typedef_gap               = 2
align_typedef_span              = 1         # The span for aligning single-line typedefs.
align_typedef_star_style        = 1
align_var_def_amp_style         = 1
align_var_def_attribute         = true
align_var_def_colon             = true      # Whether to align the colon in struct bit fields.
align_var_def_gap               = 2         # The gap (minimum spacing for aligned items) for variable definitions.
align_var_def_inline            = false
align_var_def_span              = 1         # The span (lines needed to align) for aligning variable definitions.
align_var_def_star_style        = 1         # How to consider (or treat) the '*' in the alignment of variable
                                            # definitions.
                                            # 0: Part of the type     'void *   foo;' (default)
                                            # 1: Part of the variable 'void     *foo;'
                                            # 2: Dangling             'void    *foo;'
                                            # (Note - should also set sp_after_ptr_star=remove)
align_var_struct_gap            = 4
align_var_struct_span           = 8         # The span for aligning struct/union member definitions.
align_var_struct_thresh         = 0
align_with_tabs                 = false

# Comment formatting
cmt_align_doxygen_javadoc_tags  = true      # Whether to align doxygen javadoc-style tags ('@param', '@return', etc.)
                                            # TODO: Eats '[' in '[in]'
cmt_c_group                     = false
cmt_c_nl_end                    = true      # Whether to add a newline before the closing '*/' of the combined c-comment.
cmt_c_nl_start                  = true
cmt_cpp_group                   = false
cmt_cpp_nl_end                  = true
cmt_cpp_nl_start                = true
cmt_cpp_to_c                    = false
cmt_indent_multi                = false     # Whether to apply changes to multi-line comments, including cmt_width,
                                            # keyword substitution and leading chars.
cmt_insert_before_preproc       = false
#cmt_insert_file_header          = default_file_header.txt
#cmt_insert_func_header          = default_function_header.txt
cmt_multi_check_last            = false
cmt_multi_first_len_minimum     = 2
cmt_reflow_mode                 = 1         # How to reflow comments.
                                            # (0:No reflow, 1:No touching at all, 2: Full reflow)
cmt_sp_after_star_cont          = 0         # The number of spaces to insert after the star on subsequent comment lines.
cmt_sp_before_star_cont         = 0         # The number of spaces to insert at the start of subsequent comment lines.
cmt_star_cont                   = false     # Whether to put a star on subsequent comment lines.
cmt_width                       = 120       # Try to wrap comments at N columns.
sp_cmt_cpp_start                = add       # Add or remove space after the opening of a C++ comment, as in
                                            # '// <here> A'.  NOTE: Breaks indentation within comments.

# Function definitions / declarations
indent_func_call_param          = false     # Whether to indent continued function call parameters one indent level,
                                            # rather than aligning parameters under the open parenthesis.
indent_func_class_param         = false     # Whether to indent continued function call declaration one indent level,
                                            # rather than aligning parameters under the open parenthesis.
indent_func_ctor_var_param      = false     # Whether to indent continued class variable constructors one indent level,
                                            # rather than aligning parameters under the open parenthesis.
indent_func_def_param           = true      # Whether to indent continued function definition parameters one indent
                                            # level, rather than aligning parameters under the open parenthesis.
nl_fdef_brace                   = add       # Add or remove newline between function signature and '{'.
nl_func_call_end_multi_line     = true      # Whether to add a newline before ')' in a function call if '(' and ')' are
                                            # in different lines.
nl_func_call_paren              = remove    # Add or remove newline between a function name and the opening '(' in the
                                            # call.
nl_func_call_start_multi_line   = true      # Whether to add a newline after '(' in a function call if '(' and ')' are
                                            # in different lines.
nl_func_decl_args               = force     # Add or remove newline after each ',' in a function declaration.
nl_func_decl_empty              = add       # Add or remove newline between '()' in a function declaration.
nl_func_def_args                = force     # Add or remove newline after each ',' in a function definition.
nl_func_def_empty               = add       # Add or remove newline between '()' in a function definition.
nl_func_def_paren               = remove    # Add or remove newline between a function name and the opening '('
                                            # in the definition.
nl_func_paren                   = remove    # Add or remove newline between a function name and the opening '(' in
                                            # the declaration.
nl_func_type_name               = add       # Add or remove newline between return type and function name in a function
                                            # definition.
sp_fparen_brace                 = force     # Add or remove space between ')' and '{' of function.
use_indent_func_call_param      = true      # indent_func_call_param will be used

# Additional Newline Rules
nl_after_brace_open                          = true     # Whether to add a newline after '{'. This also adds a newline
                                                        # before the matching '}'.
nl_after_brace_open_cmt                      = true     # Whether to add a newline between the open brace and a
                                                        # trailing single-line comment.
                                                        # Requires nl_after_brace_open = true.
nl_after_do                                  = add      # Add or remove blank line after 'do/while' statement.
nl_after_for                                 = add      # Add or remove blank line after 'for' statement.
nl_after_func_body                           = 2        # The number of newlines after '}' of a multi-line function body
nl_after_func_body_one_liner                 = 2
nl_after_func_proto                          = 2
nl_after_func_proto_group                    = 2
nl_after_if                                  = add
nl_after_multiline_comment                   = false
nl_after_return                              = false
nl_after_struct                              = 2
nl_after_switch                              = add
nl_after_vbrace_close                        = true
nl_after_vbrace_open                         = true
nl_after_vbrace_open_empty                   = true
nl_after_while                               = add
nl_assign_leave_one_liners                   = true
nl_before_block_comment                      = 2
nl_before_case                               = false
nl_before_do                                 = ignore
nl_before_for                                = ignore
nl_before_if                                 = ignore
nl_before_switch                             = ignore
nl_before_while                              = ignore
nl_before_whole_file_ifdef                   = 2
nl_brace_brace                               = force
nl_brace_struct_var                          = remove
nl_case_colon_brace                          = add
nl_class_leave_one_liners                    = false
nl_collapse_empty_body                       = false
nl_comment_func_def                          = 1
nl_create_for_one_liner                      = false
nl_create_if_one_liner                       = false
nl_create_while_one_liner                    = false
nl_define_macro                              = false
nl_ds_struct_enum_close_brace                = true
nl_ds_struct_enum_cmt                        = false
nl_enum_leave_one_liners                     = false
nl_func_decl_end                             = add
nl_func_decl_start                           = add
nl_func_def_end                              = add
nl_func_def_start                            = add
nl_func_leave_one_liners                     = false
nl_func_proto_type_name                      = add
nl_func_var_def_blk                          = 1
nl_getset_leave_one_liners                   = false
nl_if_leave_one_liners                       = false
nl_multi_line_define                         = false
nl_squeeze_ifdef                             = false
nl_var_def_blk_end                           = 0
nl_var_def_blk_start                         = 0

# Preprocessor Rules
pp_define_at_level      = true
pp_if_indent_code       = false
pp_indent_func_def      = false
pp_indent_extern        = false
pp_ignore_define_body   = true                # Workaround: Turn off processing for #define body
                                              # (current rules do not work for some defines)
pp_indent               = add
pp_indent_at_level      = true
pp_indent_count         = 2
pp_indent_if            = 2
pp_indent_region        = 2
pp_region_indent_code   = false
pp_space                = remove

#
# The tokens below are assigned specific types so they are always recognized properly.
#

# Explicitly define EDK II qualifiers
set QUALIFIER CONST
set QUALIFIER EFIAPI
set QUALIFIER IN
set QUALIFIER OPTIONAL
set QUALIFIER OUT

# Explicitly define EDK II types
set TYPE EFI_STATUS
set TYPE VOID
