## @file
#  Implementation for PlatformBootManagerLib library class interfaces.
#
#  Copyright (C) 2015-2016, Red Hat, Inc.
#  Copyright (c) 2014 - 2023, Arm Ltd. All rights reserved.<BR>
#  Copyright (c) 2007 - 2014, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2016, Linaro Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformBootManagerLib
  FILE_GUID                      = 92FD2DE3-B9CB-4B35-8141-42AD34D73C9F
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformBootManagerLib|DXE_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = ARM AARCH64
#

[Sources]
  PlatformBm.c
  PlatformBm.h

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  BootLogoLib
  CapsuleLib
  DebugLib
  DevicePathLib
  DxeServicesLib
  HobLib
  MemoryAllocationLib
  PcdLib
  PrintLib
  UefiBootManagerLib
  UefiBootServicesTableLib
  UefiLib
  UefiRuntimeServicesTableLib

[FeaturePcd]
  gEfiMdePkgTokenSpaceGuid.PcdUgaConsumeSupport

[FixedPcd]
  gArmTokenSpaceGuid.PcdUefiShellDefaultBootEnable
  gEfiMdeModulePkgTokenSpaceGuid.PcdFirmwareVersionString
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultBaudRate
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultDataBits
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultParity
  gEfiMdePkgTokenSpaceGuid.PcdUartDefaultStopBits
  gEfiMdePkgTokenSpaceGuid.PcdDefaultTerminalType

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdPlatformBootTimeOut
  gEfiMdeModulePkgTokenSpaceGuid.PcdBootDiscoveryPolicy
  gEfiMdeModulePkgTokenSpaceGuid.PcdEmuVariableNvModeEnable

[Guids]
  gBootDiscoveryPolicyMgrFormsetGuid
  gEdkiiNonDiscoverableEhciDeviceGuid
  gEdkiiNonDiscoverableUhciDeviceGuid
  gEdkiiNonDiscoverableXhciDeviceGuid
  gEfiBootManagerPolicyNetworkGuid
  gEfiBootManagerPolicyConnectAllGuid
  gEfiFileInfoGuid
  gEfiFileSystemInfoGuid
  gEfiFileSystemVolumeLabelInfoIdGuid
  gEfiEndOfDxeEventGroupGuid
  gEfiTtyTermGuid
  gUefiShellFileGuid

[Protocols]
  gEdkiiNonDiscoverableDeviceProtocolGuid
  gEfiBootManagerPolicyProtocolGuid
  gEfiDevicePathProtocolGuid
  gEfiGraphicsOutputProtocolGuid
  gEfiLoadedImageProtocolGuid
  gEfiPciRootBridgeIoProtocolGuid
  gEfiSimpleFileSystemProtocolGuid
  gEsrtManagementProtocolGuid
  gPlatformBootManagerProtocolGuid
