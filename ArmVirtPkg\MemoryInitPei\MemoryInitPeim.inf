## @file
#  Implementation of MemoryInitPeim that uses the first 128 MiB at the base of
#  DRAM as permanent PEI memory
#
#  Copyright (c) 2011-2014, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2022, Google LLC. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 1.27
  BASE_NAME                      = MemoryInit
  FILE_GUID                      = 0fbffd44-f98f-4e1c-9922-e9b21f13c3f8
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeMemory

[Sources]
  MemoryInitPeim.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  ArmPkg/ArmPkg.dec
  ArmPlatformPkg/ArmPlatformPkg.dec

[LibraryClasses]
  PeimEntryPoint
  DebugLib
  HobLib
  ArmLib
  ArmPlatformLib
  MemoryInitPeiLib

[Guids]
  gEfiMemoryTypeInformationGuid

[FeaturePcd]
  gEmbeddedTokenSpaceGuid.PcdPrePiProduceMemoryTypeInformationHob

[FixedPcd]
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmPlatformTokenSpaceGuid.PcdSystemMemoryUefiRegionSize

  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gEmbeddedTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode

[Depex]
  TRUE
