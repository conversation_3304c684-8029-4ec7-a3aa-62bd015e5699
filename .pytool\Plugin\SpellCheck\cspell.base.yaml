## @file
# CSpell configuration
#
# Copyright (c) Microsoft Corporation
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "version": "0.1",
    "language": "en",
    "dictionaries": [
        "companies ",
        "softwareTerms",
        "python",
        "cpp"
    ],
    "ignorePaths": [
        "*.pdb",
        "**/*_extdep/**",
        "*.pdf",
        "*.exe",
        "*.jpg"
    ],
    "minWordLength": 5,
    "allowCompoundWords": true,
    "maxNumberOfProblems": 200,
    "maxDuplicateProblems": 200,
    "ignoreWords": [
        "muchange"
    ],
    "words": [
        "ACPICA",
        "ACPINVS",
        "armltd",
        "ascii",
        "ASMLINK",
        "ATAPI",
        "autodetect",
        "autogen",
        "autoreload",
        "basetools",
        "blockio",
        "bootability",
        "bootable",
        "bootflow",
        "bootloader",
        "bootloaders",
        "bootup",
        "bringup",
        "brotli",
        "bugbug",
        "bytecodes",
        "bytelist",
        "bytestream",
        "cacheability",
        "cachetype",
        "cdrom",
        "certdb",
        "certdbv",
        "Citrix",
        "CLANGPDB",
        "classguid",
        "cmocka",
        "conout",
        "Consplitter", # common module in UEFI
        "coreboot",
        "countof",
        "cpuid",
        "creatorid",
        "cstate",
        "cygpath",
        "datacache",
        "deadloop",
        "depex",
        "depexes",
        "deregistering",
        "devicepath",
        "devicetree",
        "devpath",
        "disableif",
        "Disasm",
        "drhds",
        "dxefv",
        "dxeipl",
        "dynamicex",
        "edkii",
        "edksetup",
        "EfiSigList",
        "efivarstore",
        "EKU",
        "ENDBR",
        "endcheckbox",
        "enddate",
        "endform",
        "endformset",
        "endguidop",
        "endiannness",
        "endnumeric",
        "Endof",  # due to of not being uppercase
        "endoneof",
        "endstring",
        "Fastboot",
        "FIFOs",
        "formid",
        "formsetguid",
        "formset", #VFR
        "framebuffer",
        "fvmain",
        "fwvol",
        "FXRSTOR",
        "FXSAVE",
        "genfw",
        "Goldmont",
        "grayoutif",
        "guidid",
        "guidop",
        "guids",
        "harddisk",
        "hisilicon",
        "hoblist",
        "hwerrrec",
        "Hypercall",
        "hypercalls",
        "ideqval",
        "ideqvallist",
        "IHANDLE",
        "imagehandle",
        "initrd",
        "inmodule",
        "IOAPIC",
        "IOMMU",
        "iretw",
        "iscsi",
        "langcode",
        "langcodes",
        "langdef",
        "lastattemptstatus",
        "lastattemptversion",
        "LFENCE",
        "libraryclass",
        "littleendian",
        "lldmap",
        "lockv",
        "LOONGARCH",
        "Loongson",
        "lowestsupportedversion",
        "mainpage",
        "Mbytes",
        "mdepkg",
        "memmap",
        "Microarchitecture",
        "miniport",
        "mismanipulation",
        "MMRAM",
        "movsb",
        "MPIDR",
        "MRIOV",
        "msvcrtd",
        "Mtftp",
        "mtrrcap",
        "MTRRs",
        "multiboot",
        "mwait",
        "nasmb",
        "nmake",
        "NODEFAULTLIB",
        "nofailure",
        "nologo",
        "nonsecure",
        "NTDDI",
        "ntxml",
        "ntxmlcomparestrings",
        "ntxmlfetchcharacterdecoder",
        "ntxmlrawnextcharacter",
        "ntxmlspecialstringcompare",
        "ntxmltransformcharacter",
        "nuget",
        "numberof",
        "nvdata",
        "NVDIMM",
        "okcancel",
        "oneof",
        "oprom",
        "oproms",
        "OPTEE",
        "osruntime",
        "OVMF",
        "pagetable",
        "PCANSI",
        "PCCTS",
        "pcd's", #seems like cspell bug
        "pcencoder",
        "pclutf",
        "pcunicode",
        "pcvoid",
        "pcxml",
        "pcxmldoc",
        "pcxmlstructure",
        "pecoff",
        "peim's",
        "peims",
        "plugfest",
        "postmem",
        "preboot",
        "prefetchable",
        "premem",
        "prepi",
        "pxmldoc",
        "pxmlstructure",
        "pythonpath",
        "pytool",
        "pytools",
        "QEMU",
        "qemus",
        "qrencoder",
        "qrencoding",
        "qrlevel",
        "questionid",
        "questionref",
        "qword",
        "ramdisk",
        "RDRAND",
        "readytoboot",
        "reglist",
        "reprompt",
        "RISCV",
        "rmrrs",
        "rtlxmlcallback",
        "schedulable",
        "scrtm",
        "Sdhci",
        "selawik",
        "selftest",
        "semihalf",
        "semihost",
        "Semihosting",
        "setjump",
        "shiftn",
        "skuid",
        "SMBASE",
        "smbios",
        "smbus",
        "smram",
        "socket",
        "softfloat",
        "SRIOV",
        "StandaloneMMCore",
        "stringid",
        "subhierarchy",
        "submodule",
        "submodules",
        "subvendor",
        "suppressif",
        "swmdialogs",
        "systemtable",
        "targetlist",
        "testcase",
        "testsuites",
        "tiano",
        "tianocore",
        "tmpname",
        "toctou",
        "tokenguid",
        "tokenspace",
        "toolchain",
        "Torito",
        "Trustzone",
        "TTYTERM",
        "UARTs",
        "ucrtd",
        "uefipayload",
        "uefishelldebug",
        "unbootable",
        "uncacheable",
        "unconfigure",
        "undock",
        "undocked",
        "unenroll",
        "unenrolling",
        "unrecovered",
        "updateable",
        "uuids",
        "varstore",
        "vcruntimed",
        "Virtio",
        "watchdogtimer",
        "wbinvd",
        "whitepaper",
        "Wnonportable",
        "writeback",
        "XENSTORE",
        "xform",
        "xformed",
        "XIPFLAGS",
        "xmlef",
        "yesno"
    ]
}
