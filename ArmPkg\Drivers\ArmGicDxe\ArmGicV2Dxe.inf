#/** @file
#
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2012 - 2017, ARM Ltd. All rights reserved.<BR>
#  Copyright (c) 2025, Google LLC. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 1.30
  BASE_NAME                      = ArmGicV2Dxe
  FILE_GUID                      = 8562bf1e-f816-44fa-82d6-763f3b131e6d
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = GicV2DxeInitialize

[Sources.common]
  ArmGicCommonDxe.c
  ArmGicDxe.c
  ArmGicDxe.h
  GicV2/ArmGicV2Dxe.c

[Packages]
  ArmPkg/ArmPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
  DebugLib
  IoLib
  PcdLib
  PrintLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[Protocols]
  gHardwareInterruptProtocolGuid  ## PRODUCES
  gHardwareInterrupt2ProtocolGuid ## PRODUCES
  gEfiCpuArchProtocolGuid         ## CONSUMES

[Pcd.common]
  gArmTokenSpaceGuid.PcdGicDistributorBase
  gArmTokenSpaceGuid.PcdGicInterruptInterfaceBase

[Depex]
  gEfiCpuArchProtocolGuid
